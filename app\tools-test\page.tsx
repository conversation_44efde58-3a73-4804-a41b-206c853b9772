'use client';

import { useState } from 'react';
import { Loader2, Send, Brain, Image, Search, Globe, BarChart, LayoutDashboard, FileText, Split, MapPin, Download, Save, ExternalLink, ChevronDown, Youtube, Wand2 } from "lucide-react";
import { useSession } from 'next-auth/react';
import Markdown<PERSON>enderer from 'components/MarkdownRenderer';
import ChartTool from '../../components/ChartTool';
import DashboardTool from '../../components/DashboardTool';
import ImageGenerationProgress from '../../components/ImageGenerationProgress';
// Import the LLM Comparison Tool component
import LlmComparisonTool from './LlmComparisonTool';
// Import the YouTube Search Tool component
import YouTubeSearchTool from './YouTubeSearchTool';
// Import the Simple Prompt Agent Tool component
import SimplePromptAgentTool from './SimplePromptAgentTool';

// Define interfaces for API responses
interface LlmResponse {
  content: string;
}

interface SearchResponse {
  formattedResults: string;
}

interface ImageResponse {
  imageUrl: string;
  prompt?: string;
  model?: string;
  jobId: string;
  namespace: string;
  timestamp?: string;
  savedToGallery?: boolean;
  galleryNamespace?: string;
}

interface ExtractorResponse {
  url: string;
  title: string;
  formattedContent: string;
}

interface VisionResponse {
  content: string;
  model: string;
  imageUrl: string;
  jobId: string;
  namespace: string;
  timestamp: string;
  savedToGallery?: boolean;
}

// Combined response type with all possible properties
type ToolResponse = LlmResponse | SearchResponse | ImageResponse | ExtractorResponse | VisionResponse | null;

export default function ToolsTestPage() {
  // Get the user session
  const { data: session } = useSession();

  // Helper function to save image to gallery
  const handleSaveToGallery = async (imageData: ImageResponse) => {
    if (!session?.user) {
      alert('You must be logged in to save images to your gallery');
      return;
    }

    // Show loading indicator
    const originalButtonText = document.getElementById('save-gallery-button')?.innerHTML || 'Save to Gallery';
    if (document.getElementById('save-gallery-button')) {
      document.getElementById('save-gallery-button')!.innerHTML = '<svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg> Saving...';
    }

    try {
      const response = await fetch('/api/saveImageToGallery', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          imageUrl: imageData.imageUrl,
          prompt: imageData.prompt || 'Generated image',
          model: imageData.model || '',
          jobId: imageData.jobId,
          namespace: imageData.namespace
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to save image to gallery');
      }

      // Update the result state with the gallery namespace
      if (result && 'imageUrl' in result) {
        setResult({
          ...result,
          savedToGallery: true,
          galleryNamespace: data.namespace
        } as ImageResponse);
      }

      // Restore button text and show success message
      if (document.getElementById('save-gallery-button')) {
        document.getElementById('save-gallery-button')!.innerHTML = '<svg class="mr-2 h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg> Saved!';
      }
    } catch (error: any) {
      console.error('Error saving image to gallery:', error);
      alert(`Failed to save image to gallery: ${error.message}`);

      // Restore button text
      if (document.getElementById('save-gallery-button')) {
        document.getElementById('save-gallery-button')!.innerHTML = originalButtonText;
      }
    }
  };

  // Helper function to open the image gallery
  const openImageGallery = () => {
    window.open('/imageGallery', '_blank');
  };

  // Helper function to handle image download
  const handleImageDownload = (imageUrl: string, model: string) => {
    // Generate a filename with timestamp and model suffix
    const timestamp = Date.now();
    let modelSuffix = '';

    // Add appropriate suffix based on the model
    if (model.includes('dall-e')) {
      modelSuffix = '_dall-e-3';
    } else if (model.includes('imagen')) {
      modelSuffix = '_imagen-3';
    }

    const fileName = `image_${timestamp}${modelSuffix}.png`;

    // Show loading indicator
    const originalButtonText = document.getElementById('download-button')?.innerHTML || 'Download';
    if (document.getElementById('download-button')) {
      document.getElementById('download-button')!.innerHTML = '<svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg> Downloading...';
    }

    // Function to restore button text
    const restoreButton = () => {
      if (document.getElementById('download-button')) {
        document.getElementById('download-button')!.innerHTML = originalButtonText;
      }
    };

    // For data URLs (like from Imagen)
    if (imageUrl.startsWith('data:')) {
      try {
        const link = document.createElement('a');
        link.href = imageUrl;
        link.download = fileName;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        restoreButton();
      } catch (error) {
        console.error('Error downloading data URL image:', error);
        restoreButton();
        alert('Could not download the image automatically. Please right-click on the image and select "Save Image As..."');
      }
      return;
    }

    // For regular URLs (like from DALL-E)
    // Use XMLHttpRequest with responseType='blob' to force download
    const xhr = new XMLHttpRequest();
    xhr.open('GET', imageUrl, true);
    xhr.responseType = 'blob';

    xhr.onload = function() {
      if (this.status === 200) {
        const blob = new Blob([this.response], {type: 'image/png'});
        const url = URL.createObjectURL(blob);

        const link = document.createElement('a');
        link.href = url;
        link.download = fileName;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);
      } else {
        console.error('Error downloading image, status:', this.status);
        // Fallback method
        try {
          const link = document.createElement('a');
          link.href = imageUrl;
          link.download = fileName;
          link.target = '_blank';
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
        } catch (e) {
          console.error('Fallback download failed:', e);
          window.open(imageUrl, '_blank');
        }
      }
      restoreButton();
    };

    xhr.onerror = function() {
      console.error('Network error occurred while trying to download the image');
      restoreButton();
      // Last resort fallback
      window.open(imageUrl, '_blank');
    };

    try {
      xhr.send();
    } catch (error) {
      console.error('Error sending XHR request:', error);
      restoreButton();
      window.open(imageUrl, '_blank');
    }
  };
  // Active tab state
  const [activeTab, setActiveTab] = useState<'llm' | 'llm-comparison' | 'search' | 'image' | 'chart' | 'dashboard' | 'extractor' | 'vision' | 'youtube' | 'simple-prompt-agent'>('llm');

  // Common state
  const [loading, setLoading] = useState<boolean>(false);
  const [result, setResult] = useState<ToolResponse>(null);
  const [error, setError] = useState<string | null>(null);

  // Image generation progress state
  const [imageGenerationStep, setImageGenerationStep] = useState<number>(0);
  const [showImageProgress, setShowImageProgress] = useState<boolean>(false);

  // LLM Tool state
  const [llmPrompt, setLlmPrompt] = useState<string>('');
  const [llmContext, setLlmContext] = useState<string>('');
  const [llmProvider, setLlmProvider] = useState<string>('openai');
  const [llmModel, setLlmModel] = useState<string>('gpt-4o');

  // Define available models for each provider
  const providerModels: Record<string, string[]> = {
    'openai': ['gpt-4o', 'gpt-4.1-2025-04-14', 'o3-2025-04-16', 'o3-mini-2025-01-31', 'o1-mini-2024-09-12'],
    'anthropic': ['claude-sonnet-4-0', 'claude-opus-4-0', 'claude-3-7-sonnet', 'claude-3-5-sonnet', 'claude-3-haiku', 'claude-3-opus'],
    'groq': ['deepseek-r1-distill-llama-70b', 'llama-3.3-70b-versatile', 'meta-llama/llama-4-maverick-17b-128e-instruct'],
    'google': ['gemini-2.5-pro', 'gemini-1.5-pro', 'gemini-2.0-flash', 'gemini-1.5-flash']
  };

  // Image Generation Tool state
  const [imagePrompt, setImagePrompt] = useState<string>('');
  const [refinePrompt, setRefinePrompt] = useState<boolean>(true);
  const [imageModel, setImageModel] = useState<string>('dall-e-3');
  const [imageSize, setImageSize] = useState<string>('1024x1024');
  const [imageStyle, setImageStyle] = useState<string>('vivid');
  const [imageQuality, setImageQuality] = useState<string>('auto');
  const [imageFormat, setImageFormat] = useState<string>('jpeg');
  const [imageBackground, setImageBackground] = useState<string>('auto');
  const [imageCompression, setImageCompression] = useState<number>(80);

  // Internet Search Tool state
  const [searchType, setSearchType] = useState<'basic' | 'advanced'>('basic');

  // Basic search state
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [searchNumResults, setSearchNumResults] = useState<number>(5);

  // Advanced search state
  const [webSearchPrompt, setWebSearchPrompt] = useState<string>('');
  const [webSearchContext, setWebSearchContext] = useState<string>('');
  const [webSearchModel, setWebSearchModel] = useState<string>('gpt-4o-search-preview');
  const [searchContextSize, setSearchContextSize] = useState<'low' | 'medium' | 'high'>('medium');
  const [showLocationOptions, setShowLocationOptions] = useState<boolean>(false);
  const [country, setCountry] = useState<string>('US');
  const [city, setCity] = useState<string>('');
  const [region, setRegion] = useState<string>('');
  const [timezone, setTimezone] = useState<string>('America/New_York');
  const [forceWebSearch, setForceWebSearch] = useState<boolean>(true);

  // Web Content Extractor Tool state
  const [extractorUrl, setExtractorUrl] = useState<string>('');
  const [followLinks, setFollowLinks] = useState<boolean>(false);
  const [maxLinksToFollow, setMaxLinksToFollow] = useState<number>(3);
  const [sameOriginOnly, setSameOriginOnly] = useState<boolean>(true);

  // Vision Tool state
  const [visionImageUrl, setVisionImageUrl] = useState<string>('');
  const [visionPrompt, setVisionPrompt] = useState<string>('');
  const [visionModel, setVisionModel] = useState<string>('gpt-4o');
  const [visionTask, setVisionTask] = useState<string>('describe');
  const [imageFile, setImageFile] = useState<File | null>(null);
  const [uploadedImageUrl, setUploadedImageUrl] = useState<string>('');

  // Handle LLM Tool submission
  const handleLlmSubmit = async (): Promise<void> => {
    if (!llmPrompt) return;

    setLoading(true);
    setError(null);
    setResult(null);

    try {
      const response = await fetch('/api/tools', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          tool: 'llm',
          params: {
            prompt: llmPrompt,
            context: llmContext,
            model: llmModel,
            provider: llmProvider
          }
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to process with LLM');
      }

      setResult(data as LlmResponse);
    } catch (err: any) {
      console.error('LLM processing error:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  // Handle Image Generation Tool submission
  const handleImageSubmit = async (): Promise<void> => {
    if (!imagePrompt) return;

    setLoading(true);
    setError(null);
    setResult(null);

    // Reset and show progress indicator
    setImageGenerationStep(1);
    setShowImageProgress(true);

    try {
      // Log the current model selection
      console.log(`[UI] Selected model: ${imageModel}`);

      // Get the user ID from the session if available (use email as the identifier)
      const userId = session?.user?.email;

      // Step 1: Initializing - already set above

      // Simulate a small delay to show the initialization step
      await new Promise(resolve => setTimeout(resolve, 800));

      // Step 2: Refining prompt
      setImageGenerationStep(2);

      // Start the API request
      const response = await fetch('/api/tools', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          tool: 'image',
          params: {
            prompt: imagePrompt,
            refinePrompt: refinePrompt,
            model: imageModel,
            size: imageSize,
            style: imageStyle,
            quality: imageQuality,
            format: imageFormat,
            background: imageBackground,
            compression: imageFormat === 'png' ? undefined : imageCompression,
            userId: userId // Pass the userId for Firebase integration if available
          }
        }),
      });

      // Step 3: Generating image
      setImageGenerationStep(3);

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to generate image');
      }

      // Step 4: Processing
      setImageGenerationStep(4);

      // Simulate a small delay for processing
      await new Promise(resolve => setTimeout(resolve, 800));

      // Step 5: Complete
      setImageGenerationStep(5);

      // Set the result
      setResult(data as ImageResponse);

      // Keep the progress indicator visible for a moment so user can see completion
      setTimeout(() => {
        setShowImageProgress(false);
      }, 2000);

    } catch (err: any) {
      console.error('Image generation error:', err);
      setError(err.message);
      setShowImageProgress(false);
    } finally {
      setLoading(false);
    }
  };

  // Handle Internet Search Tool submission
  const handleSearchSubmit = async (): Promise<void> => {
    if (!searchQuery) return;

    setLoading(true);
    setError(null);
    setResult(null);

    try {
      const response = await fetch('/api/tools', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          tool: 'search',
          params: {
            query: searchQuery,
            numResults: searchNumResults
          }
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to perform internet search');
      }

      setResult(data as SearchResponse);
    } catch (err: any) {
      console.error('Internet search error:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  // Helper function to set test data for web search
  const setTestWebSearchResult = (formattedResults: string) => {
    localStorage.setItem('testWebSearchResult', JSON.stringify({ formattedResults }));
    console.log('Test web search result set in localStorage');
  };

  // Handle Advanced Web Search Tool submission
  const handleWebSearchSubmit = async (): Promise<void> => {
    if (!webSearchPrompt) return;

    setLoading(true);
    setError(null);
    setResult(null);

    try {
      // Check if we're testing with a hardcoded result (for development purposes)
      const testResult = localStorage.getItem('testWebSearchResult');
      if (testResult) {
        try {
          const parsedResult = JSON.parse(testResult);
          console.log('Using test result from localStorage:', parsedResult);

          // Process the test result through the API to ensure proper formatting
          const response = await fetch('/api/tools/web-search', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify(parsedResult),
          });

          const data = await response.json();

          if (!response.ok) {
            throw new Error(data.error || 'Failed to process test result');
          }

          setResult({ formattedResults: data.formattedResults } as SearchResponse);
          return;
        } catch (e) {
          console.error('Error parsing test result:', e);
          // Continue with normal search if test result parsing fails
        }
      }

      // Prepare user location if enabled
      const userLocation = showLocationOptions ? {
        country,
        city: city || undefined,
        region: region || undefined,
        timezone: timezone || undefined
      } : undefined;

      const response = await fetch('/api/tools/web-search', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          prompt: webSearchPrompt,
          context: webSearchContext,
          model: webSearchModel,
          searchContextSize,
          userLocation,
          forceWebSearch
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to process web search');
      }

      // Handle the result based on its type
      if (data.formattedResults) {
        // Create a SearchResponse object with the formatted results
        setResult({ formattedResults: data.formattedResults } as SearchResponse);
      } else if (typeof data.result === 'string') {
        // Backward compatibility: Create a SearchResponse object
        setResult({ formattedResults: data.result } as SearchResponse);
      } else if (data.result && typeof data.result === 'object') {
        // Backward compatibility: If it's already an object, use it directly
        setResult(data.result as SearchResponse);
      } else {
        // Convert other types to string and wrap in a SearchResponse
        setResult({ formattedResults: JSON.stringify(data.result || data) } as SearchResponse);
      }
    } catch (err: any) {
      console.error('Web search error:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  // Handle Web Content Extractor Tool submission
  const handleExtractorSubmit = async (): Promise<void> => {
    if (!extractorUrl) return;

    setLoading(true);
    setError(null);
    setResult(null);

    try {
      const response = await fetch('/api/tools', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          tool: 'extractor',
          params: {
            url: extractorUrl,
            options: {
              followLinks,
              maxLinksToFollow,
              sameOriginOnly
            }
          }
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to extract content');
      }

      setResult(data as ExtractorResponse);
    } catch (err: any) {
      console.error('Content extraction error:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  // Handle image upload for Vision tool
  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setImageFile(file);

    // Create a URL for the uploaded image
    const imageUrl = URL.createObjectURL(file);
    setUploadedImageUrl(imageUrl);
    setVisionImageUrl(imageUrl);
  };

  // Handle Vision Tool submission
  const handleVisionSubmit = async (): Promise<void> => {
    if (!uploadedImageUrl && !visionImageUrl) {
      setError('Please upload an image or provide an image URL');
      return;
    }

    setLoading(true);
    setError(null);
    setResult(null);

    try {
      // If we have a file, we need to upload it first
      let imageUrlToProcess = visionImageUrl;

      if (imageFile) {
        // Create a FormData object to send the file
        const formData = new FormData();
        formData.append('file', imageFile);

        // Upload the file
        const uploadResponse = await fetch('/api/uploadImage', {
          method: 'POST',
          body: formData,
        });

        if (!uploadResponse.ok) {
          const errorData = await uploadResponse.json();
          throw new Error(errorData.error || 'Failed to upload image');
        }

        const uploadData = await uploadResponse.json();
        imageUrlToProcess = uploadData.url;
      }

      // Get the user ID from the session if available
      const userId = session?.user?.email || undefined;

      // Process the image with the vision tool
      const response = await fetch('/api/tools', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          tool: 'vision',
          params: {
            imageUrl: imageUrlToProcess,
            prompt: visionPrompt,
            model: visionModel,
            task: visionTask,
            userId: userId
          }
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to process image');
      }

      setResult(data as VisionResponse);
    } catch (err: any) {
      console.error('Vision processing error:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-zinc-950 text-white">
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-3xl font-bold mb-2">AI Tools Testing</h1>
            <p className="text-zinc-400">Test and verify the functionality of AI tools for agentic use</p>
          </div>

          <div className="flex space-x-2">
            <a
              href="/tools"
              className="flex items-center px-4 py-2 bg-zinc-800 text-zinc-300 rounded-md hover:bg-zinc-700 focus:outline-none"
            >
              <Brain className="mr-2 text-blue-400" size={16} />
              AI Marketing Tools
            </a>
            <a
              href="/webscraper"
              className="flex items-center px-4 py-2 bg-zinc-800 text-zinc-300 rounded-md hover:bg-zinc-700 focus:outline-none"
            >
              <Globe className="mr-2 text-green-400" size={16} />
              Web Scraper
            </a>
            <a
              href="/web-extractor"
              className="flex items-center px-4 py-2 bg-zinc-800 text-zinc-300 rounded-md hover:bg-zinc-700 focus:outline-none"
            >
              <FileText className="mr-2 text-purple-400" size={16} />
              Web Extractor
            </a>
          </div>
        </div>

        {/* Tool Tabs */}
        <div className="flex mb-6 bg-zinc-900 rounded-lg overflow-hidden">
          <button
            onClick={() => {
              setActiveTab('llm');
              setResult(null);
              setError(null);
              setLoading(false);
            }}
            className={`flex-1 py-3 px-4 flex items-center justify-center ${activeTab === 'llm' ? 'bg-blue-600 text-white' : 'text-zinc-400 hover:bg-zinc-800'}`}
          >
            <Brain className="mr-2 h-5 w-5" />
            LLM Tool
          </button>
          <button
            onClick={() => {
              setActiveTab('llm-comparison');
              setResult(null);
              setError(null);
              setLoading(false);
            }}
            className={`flex-1 py-3 px-4 flex items-center justify-center ${activeTab === 'llm-comparison' ? 'bg-purple-600 text-white' : 'text-zinc-400 hover:bg-zinc-800'}`}
          >
            <Split className="mr-2 h-5 w-5" />
            LLM Comparison
          </button>
          <button
            onClick={() => {
              setActiveTab('search');
              setResult(null);
              setError(null);
              setLoading(false);
            }}
            className={`flex-1 py-3 px-4 flex items-center justify-center ${activeTab === 'search' ? 'bg-blue-600 text-white' : 'text-zinc-400 hover:bg-zinc-800'}`}
          >
            <Search className="mr-2 h-5 w-5" />
            Internet Search
          </button>

          <button
            onClick={() => {
              setActiveTab('image');
              setResult(null);
              setError(null);
              setLoading(false);
            }}
            className={`flex-1 py-3 px-4 flex items-center justify-center ${activeTab === 'image' ? 'bg-blue-600 text-white' : 'text-zinc-400 hover:bg-zinc-800'}`}
          >
            <Image className="mr-2 h-5 w-5" />
            Image Generation
          </button>
          <button
            onClick={() => {
              setActiveTab('chart');
              setResult(null);
              setError(null);
              setLoading(false);
            }}
            className={`flex-1 py-3 px-4 flex items-center justify-center ${activeTab === 'chart' ? 'bg-blue-600 text-white' : 'text-zinc-400 hover:bg-zinc-800'}`}
          >
            <BarChart className="mr-2 h-5 w-5" />
            Chart Generator
          </button>
          <button
            onClick={() => {
              setActiveTab('dashboard');
              setResult(null);
              setError(null);
              setLoading(false);
            }}
            className={`flex-1 py-3 px-4 flex items-center justify-center ${activeTab === 'dashboard' ? 'bg-blue-600 text-white' : 'text-zinc-400 hover:bg-zinc-800'}`}
          >
            <LayoutDashboard className="mr-2 h-5 w-5" />
            Dashboard
          </button>
          <button
            onClick={() => {
              setActiveTab('extractor');
              setResult(null);
              setError(null);
              setLoading(false);
            }}
            className={`flex-1 py-3 px-4 flex items-center justify-center ${activeTab === 'extractor' ? 'bg-blue-600 text-white' : 'text-zinc-400 hover:bg-zinc-800'}`}
          >
            <FileText className="mr-2 h-5 w-5" />
            Web Extractor
          </button>
          <button
            onClick={() => {
              setActiveTab('vision');
              setResult(null);
              setError(null);
              setLoading(false);
            }}
            className={`flex-1 py-3 px-4 flex items-center justify-center ${activeTab === 'vision' ? 'bg-purple-600 text-white' : 'text-zinc-400 hover:bg-zinc-800'}`}
          >
            <Image className="mr-2 h-5 w-5" />
            Vision
          </button>
          <button
            onClick={() => {
              setActiveTab('youtube');
              setResult(null);
              setError(null);
              setLoading(false);
            }}
            className={`flex-1 py-3 px-4 flex items-center justify-center ${activeTab === 'youtube' ? 'bg-red-600 text-white' : 'text-zinc-400 hover:bg-zinc-800'}`}
          >
            <Youtube className="mr-2 h-5 w-5" />
            YouTube
          </button>
          <button
            onClick={() => {
              setActiveTab('simple-prompt-agent');
              setResult(null);
              setError(null);
              setLoading(false);
            }}
            className={`flex-1 py-3 px-4 flex items-center justify-center ${activeTab === 'simple-prompt-agent' ? 'bg-purple-600 text-white' : 'text-zinc-400 hover:bg-zinc-800'}`}
          >
            <Wand2 className="mr-2 h-5 w-5" />
            Prompt Optimizer
          </button>
        </div>

        {/* LLM Tool */}
        {activeTab === 'llm' && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* LLM Tool Input Card */}
            <div className="bg-zinc-900 rounded-lg border border-zinc-700 overflow-hidden">
              <div className="p-4 border-b border-zinc-700">
                <h2 className="text-xl font-semibold text-white flex items-center">
                  <Brain className="mr-2 h-5 w-5 text-blue-400" />
                  LLM Tool
                </h2>
                <p className="text-sm text-zinc-400">
                  Process content with various language models
                </p>
              </div>
              <div className="p-4 space-y-4">
                <div className="space-y-2">
                  <label htmlFor="llm-prompt" className="block text-sm font-medium text-zinc-300">Prompt</label>
                  <textarea
                    id="llm-prompt"
                    rows={4}
                    placeholder="Enter your prompt..."
                    value={llmPrompt}
                    onChange={(e) => setLlmPrompt(e.target.value)}
                    className="w-full px-3 py-2 bg-zinc-800 border border-zinc-700 rounded-md text-white placeholder-zinc-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>

                <div className="space-y-2">
                  <label htmlFor="llm-context" className="block text-sm font-medium text-zinc-300">Context (Optional)</label>
                  <textarea
                    id="llm-context"
                    rows={2}
                    placeholder="Additional context for the prompt..."
                    value={llmContext}
                    onChange={(e) => setLlmContext(e.target.value)}
                    className="w-full px-3 py-2 bg-zinc-800 border border-zinc-700 rounded-md text-white placeholder-zinc-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <label htmlFor="llm-provider" className="block text-sm font-medium text-zinc-300">Provider</label>
                    <select
                      id="llm-provider"
                      value={llmProvider}
                      onChange={(e) => {
                        const newProvider = e.target.value;
                        setLlmProvider(newProvider);
                        // Set the first model of the selected provider as default
                        if (providerModels[newProvider] && providerModels[newProvider].length > 0) {
                          setLlmModel(providerModels[newProvider][0]);
                        }
                      }}
                      className="w-full px-3 py-2 bg-zinc-800 border border-zinc-700 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    >
                      <option value="openai">OpenAI</option>
                      <option value="anthropic">Anthropic</option>
                      <option value="google">Google</option>
                      <option value="groq">Groq</option>
                    </select>
                  </div>

                  <div className="space-y-2">
                    <label htmlFor="llm-model" className="block text-sm font-medium text-zinc-300">Model</label>
                    <select
                      id="llm-model"
                      value={llmModel}
                      onChange={(e) => setLlmModel(e.target.value)}
                      className="w-full px-3 py-2 bg-zinc-800 border border-zinc-700 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    >
                      {providerModels[llmProvider]?.map((model) => (
                        <option key={model} value={model}>
                          {model}
                        </option>
                      ))}
                    </select>
                  </div>
                </div>
              </div>
              <div className="p-4 border-t border-zinc-700">
                <button
                  onClick={handleLlmSubmit}
                  disabled={loading || !llmPrompt}
                  className={`w-full flex items-center justify-center px-4 py-2 rounded-md ${loading || !llmPrompt ? 'bg-zinc-700 text-zinc-400 cursor-not-allowed' : 'bg-blue-600 text-white hover:bg-blue-700'}`}
                >
                  {loading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Processing...
                    </>
                  ) : (
                    <>
                      <Send className="mr-2 h-4 w-4" />
                      Process with LLM
                    </>
                  )}
                </button>
              </div>
            </div>

            {/* Result Card */}
            <div className="bg-zinc-900 rounded-lg border border-zinc-700 overflow-hidden">
              <div className="p-4 border-b border-zinc-700">
                <h2 className="text-xl font-semibold text-white">LLM Response</h2>
                <p className="text-sm text-zinc-400">
                  Generated content from the language model
                </p>
              </div>
              <div className="p-4">
                {loading ? (
                  <div className="flex items-center justify-center h-[300px]">
                    <Loader2 className="h-8 w-8 animate-spin text-blue-500" />
                  </div>
                ) : error ? (
                  <div className="bg-red-900/20 p-4 rounded-md text-red-400">
                    <p className="font-semibold">Error</p>
                    <p>{error}</p>
                  </div>
                ) : result && 'content' in result ? (
                  <div className="h-[300px] overflow-y-auto">
                    <MarkdownRenderer content={result.content} />
                  </div>
                ) : (
                  <div className="flex items-center justify-center h-[300px] text-zinc-400">
                    <p>Submit a prompt to see results</p>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}

        {/* LLM Comparison Tool */}
        {activeTab === 'llm-comparison' && (
          <LlmComparisonTool />
        )}

      {/* Internet Search Tool */}
      {activeTab === 'search' && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Internet Search Tool Input Card */}
          <div className="bg-zinc-900 rounded-lg border border-zinc-700 overflow-hidden">
            <div className="p-4 border-b border-zinc-700">
              <h2 className="text-xl font-semibold text-white flex items-center">
                <Search className="mr-2 h-5 w-5 text-blue-400" />
                Internet Search Tool
              </h2>
              <p className="text-sm text-zinc-400">
                Search the web for information
              </p>

              {/* Search Type Tabs */}
              <div className="flex mt-4 border-b border-zinc-700">
                <button
                  onClick={() => {
                    setSearchType('basic');
                    setResult(null);
                    setError(null);
                    setLoading(false);
                  }}
                  className={`px-4 py-2 ${searchType === 'basic' ? 'text-blue-400 border-b-2 border-blue-400' : 'text-zinc-400 hover:text-zinc-300'}`}
                >
                  <div className="flex items-center">
                    <Search className="mr-2 h-4 w-4" />
                    Basic Search
                  </div>
                </button>
                <button
                  onClick={() => {
                    setSearchType('advanced');
                    setResult(null);
                    setError(null);
                    setLoading(false);
                  }}
                  className={`px-4 py-2 ${searchType === 'advanced' ? 'text-blue-400 border-b-2 border-blue-400' : 'text-zinc-400 hover:text-zinc-300'}`}
                >
                  <div className="flex items-center">
                    <Globe className="mr-2 h-4 w-4" />
                    Advanced (LLM) Search
                  </div>
                </button>
              </div>
            </div>
            <div className="p-4">
              {searchType === 'basic' && (
                <div className="space-y-4">
                  <div className="space-y-2">
                    <label htmlFor="search-query" className="block text-sm font-medium text-zinc-300">Search Query</label>
                    <div className="flex gap-2">
                      <input
                        id="search-query"
                        type="text"
                        placeholder="Enter your search query..."
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                        className="flex-1 px-3 py-2 bg-zinc-800 border border-zinc-700 rounded-md text-white placeholder-zinc-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      />
                      <select
                        value={searchNumResults.toString()}
                        onChange={(e) => setSearchNumResults(parseInt(e.target.value))}
                        className="w-[100px] px-3 py-2 bg-zinc-800 border border-zinc-700 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      >
                        <option value="3">3 results</option>
                        <option value="5">5 results</option>
                        <option value="8">8 results</option>
                        <option value="10">10 results</option>
                      </select>
                    </div>
                  </div>
                </div>
              )}

              {searchType === 'advanced' && (
                <div className="space-y-4">
                  <div className="space-y-2">
                    <label htmlFor="prompt" className="block text-sm font-medium text-zinc-300">Prompt</label>
                    <textarea
                      id="prompt"
                      rows={4}
                      placeholder="Enter your prompt..."
                      value={webSearchPrompt}
                      onChange={(e) => setWebSearchPrompt(e.target.value)}
                      className="w-full px-3 py-2 bg-zinc-800 border border-zinc-700 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>

                  <div className="space-y-2">
                    <label htmlFor="context" className="block text-sm font-medium text-zinc-300">Context (Optional)</label>
                    <textarea
                      id="context"
                      rows={3}
                      placeholder="Additional context for the prompt..."
                      value={webSearchContext}
                      onChange={(e) => setWebSearchContext(e.target.value)}
                      className="w-full px-3 py-2 bg-zinc-800 border border-zinc-700 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <label htmlFor="model" className="block text-sm font-medium text-zinc-300">Model</label>
                      <select
                        id="model"
                        value={webSearchModel}
                        onChange={(e) => setWebSearchModel(e.target.value)}
                        className="w-full px-3 py-2 bg-zinc-800 border border-zinc-700 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      >
                        <option value="gpt-4o-search-preview">GPT-4o Search</option>
                        <option value="gpt-4o-mini-search-preview">GPT-4o Mini Search</option>
                      </select>
                    </div>

                    <div className="space-y-2">
                      <label htmlFor="search-context-size" className="block text-sm font-medium text-zinc-300">Search Context Size</label>
                      <select
                        id="search-context-size"
                        value={searchContextSize}
                        onChange={(e) => setSearchContextSize(e.target.value as 'low' | 'medium' | 'high')}
                        className="w-full px-3 py-2 bg-zinc-800 border border-zinc-700 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      >
                        <option value="low">Low (Faster, Less Context)</option>
                        <option value="medium">Medium (Balanced)</option>
                        <option value="high">High (Slower, More Context)</option>
                      </select>
                    </div>
                  </div>

                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id="force-web-search"
                      checked={forceWebSearch}
                      onChange={(e) => setForceWebSearch(e.target.checked)}
                      className="h-4 w-4 rounded border-zinc-600 text-blue-600 focus:ring-blue-500"
                    />
                    <label htmlFor="force-web-search" className="ml-2 block text-sm text-zinc-400">
                      Force web search (ensures the model searches the web)
                    </label>
                  </div>

                  {/* Developer Tools (hidden in production) */}
                  {process.env.NODE_ENV === 'development' && (
                    <div className="mt-4 p-3 border border-dashed border-zinc-700 rounded-md">
                      <h4 className="text-sm font-medium text-zinc-300 mb-2">Developer Tools</h4>
                      <button
                        onClick={() => {
                          const testData = `The population of Enugu, Nigeria, has been reported differently across various sources:\n\n- City2Map estimated the population at 722,664 residents as of September 2024. (city2map.com)\n\n- World Population Review projected Enugu's 2025 population to be 906,658, noting a 3.55% annual growth rate. (worldpopulationreview.com)\n\n- MacroTrends reported the metro area population as 876,000 in 2024, reflecting a 3.42% increase from the previous year. (download.macrotrends.net)\n\n- Wikipedia cited the 2022 Nigerian census, stating a population of 4,690,100 across Enugu East, Enugu North, and Enugu South LGAs. (en.wikipedia.org)\n\nThese discrepancies may arise from differences in data collection methods, definitions of metropolitan boundaries, and the inclusion of surrounding areas. For the most accurate and up-to-date information, consulting official Nigerian government publications or the National Population Commission is recommended.`;
                          setTestWebSearchResult(testData);
                        }}
                        className="text-xs bg-zinc-800 hover:bg-zinc-700 text-zinc-300 px-2 py-1 rounded-md"
                      >
                        Set Test Data
                      </button>
                      <button
                        onClick={() => {
                          localStorage.removeItem('testWebSearchResult');
                          console.log('Test web search result removed from localStorage');
                        }}
                        className="text-xs bg-zinc-800 hover:bg-zinc-700 text-zinc-300 px-2 py-1 rounded-md ml-2"
                      >
                        Clear Test Data
                      </button>
                    </div>
                  )}

                  <div>
                    <div className="flex items-center mb-2">
                      <button
                        type="button"
                        onClick={() => setShowLocationOptions(!showLocationOptions)}
                        className="text-sm text-blue-400 flex items-center"
                      >
                        <MapPin className="h-4 w-4 mr-1" />
                        {showLocationOptions ? 'Hide location options' : 'Set user location'}
                      </button>
                    </div>

                    {showLocationOptions && (
                      <div className="p-3 bg-zinc-800/50 rounded-md space-y-3">
                        <div className="grid grid-cols-2 gap-3">
                          <div>
                            <label htmlFor="country" className="block text-xs font-medium text-zinc-400">Country Code</label>
                            <input
                              type="text"
                              id="country"
                              placeholder="US"
                              value={country}
                              onChange={(e) => setCountry(e.target.value)}
                              className="mt-1 w-full px-3 py-1.5 bg-zinc-700 border border-zinc-600 rounded-md text-white text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                            />
                          </div>
                          <div>
                            <label htmlFor="timezone" className="block text-xs font-medium text-zinc-400">Timezone</label>
                            <input
                              type="text"
                              id="timezone"
                              placeholder="America/New_York"
                              value={timezone}
                              onChange={(e) => setTimezone(e.target.value)}
                              className="mt-1 w-full px-3 py-1.5 bg-zinc-700 border border-zinc-600 rounded-md text-white text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                            />
                          </div>
                        </div>
                        <div className="grid grid-cols-2 gap-3">
                          <div>
                            <label htmlFor="city" className="block text-xs font-medium text-zinc-400">City</label>
                            <input
                              type="text"
                              id="city"
                              placeholder="New York"
                              value={city}
                              onChange={(e) => setCity(e.target.value)}
                              className="mt-1 w-full px-3 py-1.5 bg-zinc-700 border border-zinc-600 rounded-md text-white text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                            />
                          </div>
                          <div>
                            <label htmlFor="region" className="block text-xs font-medium text-zinc-400">Region/State</label>
                            <input
                              type="text"
                              id="region"
                              placeholder="New York"
                              value={region}
                              onChange={(e) => setRegion(e.target.value)}
                              className="mt-1 w-full px-3 py-1.5 bg-zinc-700 border border-zinc-600 rounded-md text-white text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                            />
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
            <div className="p-4 border-t border-zinc-700">
              {searchType === 'basic' && (
                <button
                  onClick={handleSearchSubmit}
                  disabled={loading || !searchQuery}
                  className={`w-full flex items-center justify-center px-4 py-2 rounded-md ${loading || !searchQuery ? 'bg-zinc-700 text-zinc-400 cursor-not-allowed' : 'bg-blue-600 text-white hover:bg-blue-700'}`}
                >
                  {loading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Searching...
                    </>
                  ) : (
                    <>
                      <Search className="mr-2 h-4 w-4" />
                      Search Web
                    </>
                  )}
                </button>
              )}

              {searchType === 'advanced' && (
                <button
                  onClick={handleWebSearchSubmit}
                  disabled={loading || !webSearchPrompt}
                  className="w-full flex justify-center items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {loading ? (
                    <>
                      <Loader2 className="animate-spin mr-2 h-5 w-5" />
                      Searching...
                    </>
                  ) : (
                    <>
                      <Globe className="mr-2 h-5 w-5" />
                      Advanced Search
                    </>
                  )}
                </button>
              )}
            </div>
          </div>

          {/* Result Card */}
          <div className="bg-zinc-900 rounded-lg border border-zinc-700 overflow-hidden">
            <div className="p-4 border-b border-zinc-700">
              <h2 className="text-xl font-semibold text-white">Search Results</h2>
              <p className="text-sm text-zinc-400">
                {searchType === 'basic' ? 'Web search results' : 'AI-powered web search results'}
              </p>
            </div>
            <div className="p-4">
              {loading ? (
                <div className="flex items-center justify-center h-[300px]">
                  <Loader2 className="h-8 w-8 animate-spin text-blue-500" />
                </div>
              ) : error ? (
                <div className="bg-red-900/20 p-4 rounded-md text-red-400">
                  <p className="font-semibold">Error</p>
                  <p>{error}</p>
                </div>
              ) : result ? (
                <div className="h-[500px] overflow-y-auto">
                  {typeof result === 'object' && result !== null && 'formattedResults' in result ? (
                    <MarkdownRenderer content={result.formattedResults} />
                  ) : (
                    <MarkdownRenderer content={typeof result === 'string' ? result : JSON.stringify(result, null, 2)} />
                  )}
                </div>
              ) : (
                <div className="flex items-center justify-center h-[300px] text-zinc-400">
                  <p>Submit a search query to see results</p>
                </div>
              )}
            </div>
          </div>
        </div>
      )}



      {/* Image Generation Tool */}
      {activeTab === 'image' && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Image Generation Tool Input Card */}
          <div className="bg-zinc-900 rounded-lg border border-zinc-700 overflow-hidden">
            <div className="p-4 border-b border-zinc-700">
              <h2 className="text-xl font-semibold text-white flex items-center">
                <Image className="mr-2 h-5 w-5 text-blue-400" />
                Image Generation Tool
              </h2>
              <p className="text-sm text-zinc-400">
                Generate images from text prompts
              </p>
            </div>
            <div className="p-4 space-y-4">
              <div className="space-y-2">
                <label htmlFor="image-prompt" className="block text-sm font-medium text-zinc-300">Image Prompt</label>
                <textarea
                  id="image-prompt"
                  rows={4}
                  placeholder="Describe the image you want to generate..."
                  value={imagePrompt}
                  onChange={(e) => setImagePrompt(e.target.value)}
                  className="w-full px-3 py-2 bg-zinc-800 border border-zinc-700 rounded-md text-white placeholder-zinc-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>

              <div className="flex items-center">
                <input
                  id="refine-prompt"
                  type="checkbox"
                  checked={refinePrompt}
                  onChange={(e) => setRefinePrompt(e.target.checked)}
                  className="h-4 w-4 rounded border-zinc-700 text-blue-600 focus:ring-blue-500 bg-zinc-800"
                />
                <label htmlFor="refine-prompt" className="ml-2 block text-sm text-zinc-300">
                  Refine prompt with AI
                </label>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <label htmlFor="image-model" className="block text-sm font-medium text-zinc-300">Model</label>
                  <select
                    id="image-model"
                    value={imageModel}
                    onChange={(e) => setImageModel(e.target.value)}
                    className="w-full px-3 py-2 bg-zinc-800 border border-zinc-700 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <optgroup label="OpenAI Models">
                      <option value="dall-e-3">DALL-E 3 (Recommended)</option>
                      <option value="gpt-image-1">GPT-Image-1</option>
                      <option value="dall-e-2">DALL-E 2</option>
                    </optgroup>
                    <optgroup label="Google Models">
                      <option value="imagen-3.0-generate-002">Imagen 3.0</option>
                    </optgroup>
                  </select>
                </div>

                <div className="space-y-2">
                  <label htmlFor="image-size" className="block text-sm font-medium text-zinc-300">Size</label>
                  <select
                    id="image-size"
                    value={imageSize}
                    onChange={(e) => setImageSize(e.target.value)}
                    className="w-full px-3 py-2 bg-zinc-800 border border-zinc-700 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="1024x1024">1024x1024 (Square)</option>
                    <option value="1536x1024">1536x1024 (Landscape)</option>
                    <option value="1024x1536">1024x1536 (Portrait)</option>
                    <option value="1792x1024">1792x1024 (Landscape)</option>
                    <option value="1024x1792">1024x1792 (Portrait)</option>
                    <option value="auto">Auto (Default)</option>
                    <option value="512x512">512x512</option>
                    <option value="256x256">256x256</option>
                  </select>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <label htmlFor="image-quality" className="block text-sm font-medium text-zinc-300">Quality</label>
                  <select
                    id="image-quality"
                    value={imageQuality}
                    onChange={(e) => setImageQuality(e.target.value)}
                    className="w-full px-3 py-2 bg-zinc-800 border border-zinc-700 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="auto">Auto (Default)</option>
                    <option value="low">Low</option>
                    <option value="medium">Medium</option>
                    <option value="high">High</option>
                    <option value="standard">Standard (DALL-E 3)</option>
                    <option value="hd">HD (DALL-E 3)</option>
                  </select>
                </div>

                <div className="space-y-2">
                  <label htmlFor="image-style" className="block text-sm font-medium text-zinc-300">Style (DALL-E 3)</label>
                  <select
                    id="image-style"
                    value={imageStyle}
                    onChange={(e) => setImageStyle(e.target.value)}
                    className="w-full px-3 py-2 bg-zinc-800 border border-zinc-700 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="vivid">Vivid</option>
                    <option value="natural">Natural</option>
                  </select>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <label htmlFor="image-format" className="block text-sm font-medium text-zinc-300">Format</label>
                  <select
                    id="image-format"
                    value={imageFormat}
                    onChange={(e) => setImageFormat(e.target.value)}
                    className="w-full px-3 py-2 bg-zinc-800 border border-zinc-700 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="png">PNG</option>
                    <option value="jpeg">JPEG</option>
                    <option value="webp">WebP</option>
                  </select>
                </div>

                <div className="space-y-2">
                  <label htmlFor="image-background" className="block text-sm font-medium text-zinc-300">Background</label>
                  <select
                    id="image-background"
                    value={imageBackground}
                    onChange={(e) => setImageBackground(e.target.value)}
                    className="w-full px-3 py-2 bg-zinc-800 border border-zinc-700 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="auto">Auto (Default)</option>
                    <option value="transparent">Transparent</option>
                    <option value="opaque">Opaque</option>
                  </select>
                </div>
              </div>

              <div className="space-y-2">
                <label htmlFor="image-compression" className="block text-sm font-medium text-zinc-300">
                  Compression ({imageCompression}%) - For JPEG/WebP
                </label>
                <input
                  id="image-compression"
                  type="range"
                  min="0"
                  max="100"
                  value={imageCompression}
                  onChange={(e) => setImageCompression(parseInt(e.target.value))}
                  className="w-full h-2 bg-zinc-700 rounded-lg appearance-none cursor-pointer"
                />
                <div className="flex justify-between text-xs text-zinc-400">
                  <span>0% (Highest Quality)</span>
                  <span>100% (Smallest Size)</span>
                </div>
              </div>
            </div>
            <div className="p-4 border-t border-zinc-700">
              <button
                onClick={handleImageSubmit}
                disabled={loading || !imagePrompt}
                className={`w-full flex items-center justify-center px-4 py-2 rounded-md ${loading || !imagePrompt ? 'bg-zinc-700 text-zinc-400 cursor-not-allowed' : 'bg-blue-600 text-white hover:bg-blue-700'}`}
              >
                {loading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Generating...
                  </>
                ) : (
                  <>
                    <Image className="mr-2 h-4 w-4" />
                    Generate Image
                  </>
                )}
              </button>
            </div>
          </div>

          {/* Result Card */}
          <div className="bg-zinc-900 rounded-lg border border-zinc-700 overflow-hidden">
            <div className="p-4 border-b border-zinc-700">
              <h2 className="text-xl font-semibold text-white">Generated Image</h2>
              <p className="text-sm text-zinc-400">
                AI-generated image from your prompt
              </p>
            </div>
            <div className="p-4">
              {/* Show progress indicator when generating image */}
              {showImageProgress && (
                <ImageGenerationProgress currentStep={imageGenerationStep} />
              )}

              {loading ? (
                <div className="flex items-center justify-center h-[300px]">
                  <Loader2 className="h-8 w-8 animate-spin text-blue-500" />
                </div>
              ) : error ? (
                <div className="bg-red-900/20 p-4 rounded-md text-red-400">
                  <p className="font-semibold">Error</p>
                  <p>{error}</p>
                </div>
              ) : result && 'imageUrl' in result ? (
                <div className="space-y-4">
                  <div className="relative aspect-square max-h-[300px] overflow-hidden rounded-md border border-zinc-700">
                    <img
                      src={result.imageUrl}
                      alt="Generated image"
                      className="object-contain w-full h-full"
                    />
                  </div>
                  <div className="flex flex-col space-y-3">
                    <div className="text-sm text-zinc-400">
                      <p><span className="font-semibold text-zinc-300">Job ID:</span> {result.jobId}</p>
                      <p><span className="font-semibold text-zinc-300">Namespace:</span> {result.namespace}</p>
                      <p><span className="font-semibold text-zinc-300">Model:</span> {result.model || (result.namespace === 'google-imagen' ? 'imagen-3.0' : result.namespace === 'openai' ? 'dall-e-3' : 'Unknown')}</p>
                    </div>
                    <div className="flex justify-between items-center">
                      <div className="flex space-x-2">
                        <button
                          id="download-button"
                          onClick={() => handleImageDownload(result.imageUrl, result.model || result.namespace || 'unknown')}
                          className="flex items-center px-3 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md transition-colors"
                          title={`Download image generated by ${result.model || result.namespace || 'AI'}`}
                        >
                          <Download className="mr-2 h-4 w-4" />
                          Download
                        </button>

                        {'savedToGallery' in result && !result.savedToGallery ? (
                          <button
                            id="save-gallery-button"
                            onClick={() => handleSaveToGallery(result as ImageResponse)}
                            className="flex items-center px-3 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-md transition-colors"
                            title="Save this image to your gallery"
                            disabled={!session?.user}
                          >
                            <Save className="mr-2 h-4 w-4" />
                            Save to Gallery
                          </button>
                        ) : (
                          <button
                            onClick={openImageGallery}
                            className="flex items-center px-3 py-2 bg-green-600 hover:bg-green-700 text-white rounded-md transition-colors"
                            title="View your image gallery"
                          >
                            <ExternalLink className="mr-2 h-4 w-4" />
                            View in Gallery
                          </button>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="flex items-center justify-center h-[300px] text-zinc-400">
                  <p>Submit a prompt to generate an image</p>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Chart Generation Tool */}
      {activeTab === 'chart' && (
        <div className="p-4">
          <ChartTool />
        </div>
      )}

      {/* Dashboard Tool */}
      {activeTab === 'dashboard' && (
        <div className="p-4">
          <DashboardTool />
        </div>
      )}

      {/* Web Content Extractor Tool */}
      {activeTab === 'extractor' && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Web Content Extractor Tool Input Card */}
          <div className="bg-zinc-900 rounded-lg border border-zinc-700 overflow-hidden">
            <div className="p-4 border-b border-zinc-700">
              <h2 className="text-xl font-semibold text-white flex items-center">
                <FileText className="mr-2 h-5 w-5 text-blue-400" />
                Web Content Extractor
              </h2>
              <p className="text-sm text-zinc-400">
                Extract and format content from websites
              </p>
            </div>
            <div className="p-4 space-y-4">
              <div className="space-y-2">
                <label htmlFor="extractor-url" className="block text-sm font-medium text-zinc-300">URL</label>
                <input
                  id="extractor-url"
                  type="text"
                  placeholder="https://example.com"
                  value={extractorUrl}
                  onChange={(e) => setExtractorUrl(e.target.value)}
                  className="w-full px-3 py-2 bg-zinc-800 border border-zinc-700 rounded-md text-white placeholder-zinc-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>

              <div className="flex items-center space-x-2">
                <input
                  id="follow-links"
                  type="checkbox"
                  checked={followLinks}
                  onChange={(e) => setFollowLinks(e.target.checked)}
                  className="h-4 w-4 rounded border-zinc-700 text-blue-600 focus:ring-blue-500 bg-zinc-800"
                />
                <label htmlFor="follow-links" className="text-sm text-zinc-300">
                  Follow Links
                </label>
              </div>

              {followLinks && (
                <div className="pl-6 space-y-3">
                  <div className="space-y-2">
                    <label htmlFor="max-links" className="block text-sm font-medium text-zinc-300">Max Links to Follow</label>
                    <input
                      id="max-links"
                      type="number"
                      min="1"
                      max="10"
                      value={maxLinksToFollow}
                      onChange={(e) => setMaxLinksToFollow(parseInt(e.target.value) || 3)}
                      className="w-full px-3 py-2 bg-zinc-800 border border-zinc-700 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>

                  <div className="flex items-center space-x-2">
                    <input
                      id="same-origin"
                      type="checkbox"
                      checked={sameOriginOnly}
                      onChange={(e) => setSameOriginOnly(e.target.checked)}
                      className="h-4 w-4 rounded border-zinc-700 text-blue-600 focus:ring-blue-500 bg-zinc-800"
                    />
                    <label htmlFor="same-origin" className="text-sm text-zinc-300">
                      Same Origin Only
                    </label>
                  </div>
                </div>
              )}
            </div>
            <div className="p-4 border-t border-zinc-700">
              <button
                onClick={handleExtractorSubmit}
                disabled={loading || !extractorUrl}
                className={`w-full flex items-center justify-center px-4 py-2 rounded-md ${loading || !extractorUrl ? 'bg-zinc-700 text-zinc-400 cursor-not-allowed' : 'bg-blue-600 text-white hover:bg-blue-700'}`}
              >
                {loading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Extracting...
                  </>
                ) : (
                  <>
                    <FileText className="mr-2 h-4 w-4" />
                    Extract Content
                  </>
                )}
              </button>
            </div>
          </div>

          {/* Result Card */}
          <div className="bg-zinc-900 rounded-lg border border-zinc-700 overflow-hidden">
            <div className="p-4 border-b border-zinc-700">
              <h2 className="text-xl font-semibold text-white">Extracted Content</h2>
              <p className="text-sm text-zinc-400">
                Formatted content from the website
              </p>
            </div>
            <div className="p-4">
              {loading ? (
                <div className="flex items-center justify-center h-[300px]">
                  <Loader2 className="h-8 w-8 animate-spin text-blue-500" />
                </div>
              ) : error ? (
                <div className="bg-red-900/20 p-4 rounded-md text-red-400">
                  <p className="font-semibold">Error</p>
                  <p>{error}</p>
                </div>
              ) : result && 'formattedContent' in result ? (
                <div className="space-y-4">
                  <div>
                    <h3 className="text-lg font-semibold">{result.title}</h3>
                    <p className="text-sm text-zinc-400">{result.url}</p>
                  </div>
                  <div className="h-[300px] overflow-y-auto">
                    <MarkdownRenderer content={result.formattedContent} />
                  </div>
                </div>
              ) : (
                <div className="flex items-center justify-center h-[300px] text-zinc-400">
                  <p>Enter a URL to extract content</p>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Vision Tool */}
      {activeTab === 'vision' && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Vision Tool Input Card */}
          <div className="bg-zinc-900 rounded-lg border border-zinc-700 overflow-hidden">
            <div className="p-4 border-b border-zinc-700">
              <h2 className="text-xl font-semibold text-white flex items-center">
                <Image className="mr-2 h-5 w-5 text-purple-400" />
                Vision Tool
              </h2>
              <p className="text-sm text-zinc-400">
                Analyze images with various vision models
              </p>
            </div>
            <div className="p-4 space-y-4">
              {/* Image Upload */}
              <div className="space-y-2">
                <label className="block text-sm font-medium text-zinc-300">Upload Image</label>
                <div className="relative flex flex-col items-center justify-center w-full h-32 px-4 transition bg-zinc-800 border-2 border-zinc-700 border-dashed rounded-md appearance-none cursor-pointer hover:border-zinc-500 focus:outline-none">
                  <input
                    type="file"
                    accept="image/*"
                    className="absolute inset-0 w-full h-full opacity-0 cursor-pointer z-10"
                    onChange={handleImageUpload}
                    onClick={(e) => e.stopPropagation()}
                  />
                  {uploadedImageUrl ? (
                    <img
                      src={uploadedImageUrl}
                      alt="Uploaded preview"
                      className="h-full object-contain"
                    />
                  ) : (
                    <div className="flex flex-col items-center justify-center pt-5 pb-6 pointer-events-none">
                      <Image className="w-8 h-8 mb-2 text-zinc-500" />
                      <p className="mb-2 text-sm text-zinc-500">
                        <span className="font-semibold">Click to upload</span> or drag and drop
                      </p>
                      <p className="text-xs text-zinc-500">PNG, JPG, WEBP (MAX. 10MB)</p>
                    </div>
                  )}
                </div>
              </div>

              {/* Image URL */}
              <div className="space-y-2">
                <label htmlFor="vision-image-url" className="block text-sm font-medium text-zinc-300">
                  Or Enter Image URL
                </label>
                <input
                  id="vision-image-url"
                  type="url"
                  placeholder="https://example.com/image.jpg"
                  value={visionImageUrl}
                  onChange={(e) => setVisionImageUrl(e.target.value)}
                  className="w-full px-3 py-2 bg-zinc-800 border border-zinc-700 rounded-md text-white placeholder-zinc-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>

              {/* Custom Prompt */}
              <div className="space-y-2">
                <label htmlFor="vision-prompt" className="block text-sm font-medium text-zinc-300">
                  Custom Prompt (Optional)
                </label>
                <textarea
                  id="vision-prompt"
                  rows={3}
                  placeholder="Describe what you see in this image..."
                  value={visionPrompt}
                  onChange={(e) => setVisionPrompt(e.target.value)}
                  className="w-full px-3 py-2 bg-zinc-800 border border-zinc-700 rounded-md text-white placeholder-zinc-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 custom-scrollbar"
                />
              </div>

              {/* Model Selection */}
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <label htmlFor="vision-model" className="block text-sm font-medium text-zinc-300">Model</label>
                  <select
                    id="vision-model"
                    value={visionModel}
                    onChange={(e) => setVisionModel(e.target.value)}
                    className="w-full px-3 py-2 bg-zinc-800 border border-zinc-700 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="gpt-4o">GPT-4o</option>
                    <option value="gpt-4.1-mini">GPT-4.1-mini</option>
                    <option value="gemini-2.0-flash">Gemini 2.0 Flash</option>
                    <option value="meta-llama/llama-4-maverick-17b-128e-instruct">Llama-4-Maverick</option>
                  </select>
                </div>

                <div className="space-y-2">
                  <label htmlFor="vision-task" className="block text-sm font-medium text-zinc-300">Task</label>
                  <select
                    id="vision-task"
                    value={visionTask}
                    onChange={(e) => setVisionTask(e.target.value)}
                    className="w-full px-3 py-2 bg-zinc-800 border border-zinc-700 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="describe">Describe Image</option>
                    <option value="analyze">Detailed Analysis</option>
                    <option value="extract-text">Extract Text</option>
                    <option value="custom">Custom Prompt</option>
                  </select>
                </div>
              </div>
            </div>
            <div className="p-4 border-t border-zinc-700">
              <button
                onClick={handleVisionSubmit}
                disabled={loading || (!uploadedImageUrl && !visionImageUrl)}
                className={`w-full flex items-center justify-center px-4 py-2 rounded-md ${loading || (!uploadedImageUrl && !visionImageUrl) ? 'bg-zinc-700 text-zinc-400 cursor-not-allowed' : 'bg-purple-600 text-white hover:bg-purple-700'}`}
              >
                {loading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Processing...
                  </>
                ) : (
                  <>
                    <Image className="mr-2 h-4 w-4" />
                    Analyze Image
                  </>
                )}
              </button>
            </div>
          </div>

          {/* Result Card */}
          <div className="bg-zinc-900 rounded-lg border border-zinc-700 overflow-hidden">
            <div className="p-4 border-b border-zinc-700">
              <h2 className="text-xl font-semibold text-white">Vision Analysis</h2>
              <p className="text-sm text-zinc-400">
                Analysis results from the vision model
              </p>
            </div>
            <div className="p-4">
              {loading ? (
                <div className="flex items-center justify-center h-[550px]">
                  <Loader2 className="h-8 w-8 animate-spin text-purple-500" />
                </div>
              ) : error ? (
                <div className="bg-red-900/20 p-4 rounded-md text-red-400">
                  <p className="font-semibold">Error</p>
                  <p>{error}</p>
                </div>
              ) : result && 'content' in result && 'imageUrl' in result ? (
                <div className="flex flex-col h-[550px]">
                  {/* Fixed image and model info section - with fixed height */}
                  <div className="flex flex-col items-center mb-3 flex-shrink-0" style={{ maxHeight: '150px' }}>
                    {result.imageUrl && (
                      <div className="mb-1 max-h-32 overflow-hidden">
                        <img
                          src={result.imageUrl}
                          alt="Analyzed image"
                          className="max-h-32 object-contain"
                        />
                      </div>
                    )}
                    <div className="text-xs text-zinc-400">
                      Analyzed with {result.model}
                    </div>
                  </div>

                  {/* Scrollable content section */}
                  <div className="relative flex-grow">
                    <div className="absolute inset-0 overflow-y-auto custom-scrollbar pr-2">
                      <div className="prose prose-invert max-w-none prose-p:text-base prose-p:leading-relaxed">
                        <MarkdownRenderer content={result.content} />
                      </div>
                      {/* Add extra padding at the bottom to ensure content is clearly scrollable */}
                      <div className="h-12"></div>
                    </div>
                    <div className="absolute bottom-0 left-0 right-0 h-14 bg-gradient-to-t from-zinc-900 to-transparent pointer-events-none flex justify-center items-end pb-2">
                      <ChevronDown className="h-5 w-5 text-purple-400 animate-bounce" />
                    </div>
                  </div>
                </div>
              ) : (
                <div className="flex items-center justify-center h-[550px] text-zinc-400">
                  <p>Upload or provide an image URL to analyze</p>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* YouTube Search Tool */}
      {activeTab === 'youtube' && (
        <YouTubeSearchTool />
      )}

      {/* Prompt Optimizer Tool */}
      {activeTab === 'simple-prompt-agent' && (
        <SimplePromptAgentTool />
      )}
      </div>
    </div>
  );
}