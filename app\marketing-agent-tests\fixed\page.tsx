'use client';

import React, { useState } from 'react';
import { useSession } from 'next-auth/react';
import FixedAgentCollaborationTab from '../../../components/marketing/FixedAgentCollaborationTab';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../../../components/ui/tabs';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '../../../components/ui/card';
import { Button } from '../../../components/ui/button';
import { AlertCircle, FileText, Users, BarChart, Layers, Settings } from 'lucide-react';

const FixedMarketingAgentTests = () => {
  const { data: session, status } = useSession();
  const [activeTab, setActiveTab] = useState('collaboration');
  const [selectedProvider, setSelectedProvider] = useState<'openai' | 'anthropic' | 'groq' | 'google'>('google');
  const [selectedModel, setSelectedModel] = useState('gemini-2.5-pro');

  // If the user is not authenticated, show a login message
  if (status === 'loading') {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-900">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-500"></div>
      </div>
    );
  }

  if (status === 'unauthenticated') {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen bg-gray-900 p-4">
        <AlertCircle className="h-16 w-16 text-red-500 mb-4" />
        <h1 className="text-2xl font-bold text-white mb-2">Authentication Required</h1>
        <p className="text-gray-300 mb-6 text-center">You need to be logged in to access the Marketing Agent Testing Suite.</p>
        <Button
          onClick={() => window.location.href = '/api/auth/signin'}
          className="bg-purple-600 hover:bg-purple-700 text-white"
        >
          Sign In
        </Button>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-900 p-4 md:p-8">
      <header className="mb-8">
        <div className="flex flex-col md:flex-row items-start md:items-center justify-between mb-4">
          <div className="flex items-center mb-4 md:mb-0">
            <div className="mr-4 text-purple-500">
              <svg width="40" height="40" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 2L2 7L12 12L22 7L12 2Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                <path d="M2 17L12 22L22 17" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                <path d="M2 12L12 17L22 12" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
            </div>
            <div>
              <h1 className="text-2xl md:text-3xl font-bold text-white">Marketing Agent Testing Suite (Fixed)</h1>
              <p className="text-gray-400">Test and evaluate marketing agent performance</p>
            </div>
          </div>
          <div className="flex items-center space-x-2 bg-gray-800 p-2 rounded-lg border border-gray-700">
            <div className="flex items-center">
              <img src={session?.user?.image || '/placeholder-avatar.png'} alt={session?.user?.name || 'User'} className="w-8 h-8 rounded-full mr-2" />
              <span className="text-gray-300 text-sm">{session?.user?.email}</span>
            </div>
          </div>
        </div>
      </header>

      <div className="mb-6">
        <div className="bg-gray-800 rounded-lg shadow-lg p-6 border border-gray-700">
          <h2 className="text-xl font-semibold mb-6 text-purple-200">Strategic Marketing Agent Analysis (Fixed)</h2>
          <p className="text-gray-300 mb-4">
            This is a fixed version of the Strategic Director Agent that includes improved error handling and debugging information.
            Use this to diagnose issues with the agent&apos;s responses.
          </p>
          <div className="mb-6 bg-gray-700 p-4 rounded-lg">
            <h3 className="text-lg font-medium mb-3 text-purple-300">Model Configuration</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium mb-1 text-gray-300">Provider</label>
                <select
                  value={selectedProvider}
                  onChange={(e) => setSelectedProvider(e.target.value as any)}
                  className="w-full p-2 bg-gray-800 text-white border border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                >
                  <option value="google">Google</option>
                  <option value="anthropic">Anthropic</option>
                  <option value="openai">OpenAI</option>
                  <option value="groq">Groq</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium mb-1 text-gray-300">Model</label>
                <select
                  value={selectedModel}
                  onChange={(e) => setSelectedModel(e.target.value)}
                  className="w-full p-2 bg-gray-800 text-white border border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                >
                  {selectedProvider === 'google' && (
                    <>
                      <option value="gemini-1.5-pro">gemini-1.5-pro</option>
                      <option value="gemini-1.5-flash">gemini-1.5-flash</option>
                      <option value="gemini-2.0-flash">gemini-2.0-flash</option>
                      <option value="gemini-2.5-pro">gemini-2.5-pro</option>
                    </>
                  )}
                  {selectedProvider === 'anthropic' && (
                    <>
                      <option value="claude-3-opus-20240229">claude-3-opus-20240229</option>
                      <option value="claude-3-sonnet-20240229">claude-3-sonnet-20240229</option>
                      <option value="claude-3-haiku-20240307">claude-3-haiku-20240307</option>
                    </>
                  )}
                  {selectedProvider === 'openai' && (
                    <>
                      <option value="gpt-4">gpt-4</option>
                      <option value="gpt-4-turbo">gpt-4-turbo</option>
                      <option value="gpt-4-32k">gpt-4-32k</option>
                      <option value="gpt-3.5-turbo">gpt-3.5-turbo</option>
                    </>
                  )}
                  {selectedProvider === 'groq' && (
                    <>
                      <option value="llama3-70b-8192">llama3-70b-8192</option>
                      <option value="llama3-8b-8192">llama3-8b-8192</option>
                      <option value="mixtral-8x7b-32768">mixtral-8x7b-32768</option>
                    </>
                  )}
                </select>
              </div>
            </div>
          </div>
          <FixedAgentCollaborationTab
            selectedProvider={selectedProvider}
            selectedModel={selectedModel}
          />
        </div>
      </div>
    </div>
  );
};

export default FixedMarketingAgentTests;
