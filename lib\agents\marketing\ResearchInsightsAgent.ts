/**
 * Research & Insights Agent
 *
 * This agent provides critical intelligence that informs marketing strategy:
 * - Conducts competitive analysis
 * - Identifies target audience segments and preferences
 * - Analyzes market trends and opportunities
 * - Gathers data on content performance
 * - Provides ongoing intelligence to refine strategy
 */

import { MarketingAgent } from './MarketingAgent';
import { LlmProvider } from '../../tools/llm-tool';
import { chartTool, ChartGenerationResult, CHART_TYPES } from '../../tools/chart-tool';
import { dateTimeTool } from '../../tools/dateTimeTool';
import { z } from 'zod';

export interface CompetitiveAnalysis {
  id: string;
  productName: string;
  competitors: Array<{
    name: string;
    strengths: string[];
    weaknesses: string[];
    marketShare?: number;
    pricing?: string;
    uniqueSellingPoints: string[];
  }>;
  marketGaps: string[];
  opportunities: string[];
  threats: string[];
  createdAt: Date;
  formattedTimestamp?: string;
}

export interface AudienceSegment {
  id: string;
  name: string;
  demographics: Record<string, any>;
  psychographics: string[];
  behaviors: string[];
  painPoints: string[];
  goals: string[];
  preferredChannels: string[];
  createdAt: Date;
}

export interface MarketTrend {
  id: string;
  name: string;
  description: string;
  impact: 'low' | 'medium' | 'high';
  timeframe: 'short-term' | 'medium-term' | 'long-term';
  relevantIndustries: string[];
  opportunities: string[];
  threats: string[];
  sources: string[];
  createdAt: Date;
}

// Define Zod schemas for validation
const CompetitorSchema = z.object({
  name: z.string(),
  strengths: z.array(z.string()),
  weaknesses: z.array(z.string()),
  marketShare: z.number().optional(),
  pricing: z.string().optional(),
  uniqueSellingPoints: z.array(z.string())
});

const CompetitiveAnalysisSchema = z.object({
  competitors: z.array(CompetitorSchema),
  marketGaps: z.array(z.string()),
  opportunities: z.array(z.string()),
  threats: z.array(z.string())
});

export class ResearchInsightsAgent extends MarketingAgent {
  private competitiveAnalyses: CompetitiveAnalysis[] = [];
  private audienceSegments: AudienceSegment[] = [];
  private marketTrends: MarketTrend[] = [];

  constructor(
    id: string = 'research-insights',
    name: string = 'Research & Insights',
    userId: string = '',
    defaultLlmProvider: LlmProvider = 'google',
    defaultLlmModel: string = 'gemini-2.5-pro-preview-05-06'
  ) {
    const role = 'Research & Insights Specialist';
    const description = `As the Research & Insights Specialist, I am responsible for gathering and analyzing
market data, conducting competitive analysis, identifying target audience segments, tracking market trends,
and providing actionable insights to inform marketing strategy.`;

    super(id, name, role, description, userId, defaultLlmProvider, defaultLlmModel);

    // Initialize memory structure
    if (!this.memory.Agent_Response) {
      this.memory.Agent_Response = {};
    }

    // Initialize all required memory structures
    if (!this.memory.Agent_Response.marketResearchReports) {
      this.memory.Agent_Response.marketResearchReports = [];
    }

    if (!this.memory.Agent_Response.marketingResearch) {
      this.memory.Agent_Response.marketingResearch = [];
    }

    if (!this.memory.Agent_Response.competitiveAnalyses) {
      this.memory.Agent_Response.competitiveAnalyses = [];
    }

    if (!this.memory.Agent_Response.audienceSegments) {
      this.memory.Agent_Response.audienceSegments = [];
    }

    if (!this.memory.Agent_Response.marketTrends) {
      this.memory.Agent_Response.marketTrends = [];
    }
  }

  /**
   * Ensure memory structures are properly initialized
   * This helps prevent Firebase errors when saving memory
   */
  private ensureMemoryStructures(): void {
    // Initialize Agent_Response memory if it doesn't exist
    if (!this.memory.Agent_Response) {
      this.memory.Agent_Response = {};
    }

    // Initialize Agent_Response memory structures if they don't exist
    if (!this.memory.Agent_Response.marketResearchReports) {
      this.memory.Agent_Response.marketResearchReports = [];
    }

    if (!this.memory.Agent_Response.marketingResearch) {
      this.memory.Agent_Response.marketingResearch = [];
    }

    if (!this.memory.Agent_Response.competitiveAnalyses) {
      this.memory.Agent_Response.competitiveAnalyses = [];
    }

    if (!this.memory.Agent_Response.audienceSegments) {
      this.memory.Agent_Response.audienceSegments = [];
    }

    if (!this.memory.Agent_Response.marketTrends) {
      this.memory.Agent_Response.marketTrends = [];
    }
  }

  /**
   * Override saveMemoryToStorage to ensure memory structures are initialized
   */
  protected async saveMemoryToStorage(): Promise<void> {
    // Ensure memory structures are properly initialized before saving
    this.ensureMemoryStructures();

    // Call the parent class implementation
    await super.saveMemoryToStorage();
  }

  /**
   * Extract JSON from a string that might contain markdown or other text
   * @param text - The text that might contain JSON
   * @returns The extracted JSON string or the original text if no JSON is found
   */
  private extractJsonFromText(text: string): string {
    // First, check if the response is wrapped in markdown code blocks
    const markdownJsonRegex = /```(?:json)?\s*([\s\S]*?)```/;
    const markdownMatch = markdownJsonRegex.exec(text);
    if (markdownMatch && markdownMatch[1]) {
      console.log(`[ResearchInsightsAgent] Detected markdown code block, extracting JSON content`);
      return markdownMatch[1].trim();
    }

    // If no markdown blocks, try to extract JSON using regex
    const jsonRegex = /{[\s\S]*}/;
    const jsonMatch = jsonRegex.exec(text);
    if (jsonMatch) {
      console.log(`[ResearchInsightsAgent] Extracted JSON object using regex`);
      return jsonMatch[0];
    }

    // Return the original text if no JSON pattern is found
    return text;
  }

  /**
   * Conduct competitive analysis for a product
   */
  async conductCompetitiveAnalysis(productName: string, industry: string): Promise<any> {
    console.log(`[ResearchInsightsAgent] Starting competitive analysis for product: "${productName}" in industry: "${industry}"`);

    try {
      // Create a prompt for competitive analysis
      const prompt = `
      Conduct a comprehensive competitive analysis for the following product:

      Product Name: ${productName}
      Industry: ${industry}

      Please identify:
      1. Top 3-5 competitors in this space
      2. Each competitor's strengths and weaknesses
      3. Their unique selling points
      4. Market gaps that could be exploited
      5. Opportunities and threats in the competitive landscape

      IMPORTANT: Format your response as a valid JSON object with these sections as keys.
      DO NOT include any markdown formatting, code blocks, or explanatory text.
      ONLY return the raw JSON object.

      The expected format is:
      {
        "competitors": [
          {
            "name": "Competitor Name",
            "strengths": ["Strength 1", "Strength 2"],
            "weaknesses": ["Weakness 1", "Weakness 2"],
            "uniqueSellingPoints": ["USP 1", "USP 2"]
          }
        ],
        "marketGaps": ["Gap 1", "Gap 2"],
        "opportunities": ["Opportunity 1", "Opportunity 2"],
        "threats": ["Threat 1", "Threat 2"]
      }
      `;

      // Process with LLM
      console.log(`[ResearchInsightsAgent] Sending competitive analysis prompt to LLM`);
      const analysisJson = await this.processRequest(prompt);
      console.log(`[ResearchInsightsAgent] Received response from LLM, attempting to parse JSON`);

      try {
        // Extract JSON from the response
        const cleanJson = this.extractJsonFromText(analysisJson);
        console.log(`[ResearchInsightsAgent] Attempting to parse cleaned JSON`);

        // Parse the JSON response
        const parsedJson = JSON.parse(cleanJson);

        // Validate with Zod schema
        console.log(`[ResearchInsightsAgent] Validating JSON with Zod schema`);
        const validationResult = CompetitiveAnalysisSchema.safeParse(parsedJson);

        if (!validationResult.success) {
          console.error(`[ResearchInsightsAgent] Zod validation failed:`, validationResult.error);
          return {
            success: false,
            error: `Invalid competitive analysis data: ${validationResult.error.message}`,
            validationErrors: validationResult.error.format(),
            rawResponse: analysisJson
          };
        }

        // Extract the validated data
        const analysisData = validationResult.data;
        console.log(`[ResearchInsightsAgent] Successfully validated JSON data`);

        // Create the competitive analysis object
        const competitiveAnalysis: CompetitiveAnalysis = {
          id: `comp-analysis-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
          productName,
          competitors: analysisData.competitors,
          marketGaps: analysisData.marketGaps,
          opportunities: analysisData.opportunities,
          threats: analysisData.threats,
          createdAt: new Date(),
          formattedTimestamp: dateTimeTool.getCurrentDateTime({
            format: 'medium',
            includeTime: true
          })
        };

        console.log(`[ResearchInsightsAgent] Created competitive analysis object with ${competitiveAnalysis.competitors.length} competitors`);
        this.competitiveAnalyses.push(competitiveAnalysis);

        // Store in memory for context
        if (!this.memory.Agent_Response.competitiveAnalyses) {
          this.memory.Agent_Response.competitiveAnalyses = [];
        }
        this.memory.Agent_Response.competitiveAnalyses.push(competitiveAnalysis);

        return {
          success: true,
          data: competitiveAnalysis
        };
      } catch (parseError) {
        console.error('[ResearchInsightsAgent] Error parsing competitive analysis JSON:', parseError);
        console.error('[ResearchInsightsAgent] Raw response that failed to parse:', analysisJson.substring(0, 500) + '...');

        // Try to extract any useful information from the failed response
        return {
          success: false,
          error: 'Failed to parse competitive analysis response: ' + (parseError instanceof Error ? parseError.message : 'Unknown parsing error'),
          rawResponse: analysisJson
        };
      }
    } catch (error) {
      console.error('[ResearchInsightsAgent] Error in conductCompetitiveAnalysis:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred during competitive analysis',
      };
    }
  }

  /**
   * Identify target audience segments
   */
  async identifyAudienceSegments(productName: string, industry: string, productDescription: string): Promise<AudienceSegment[]> {
    // Create a prompt for audience segmentation
    const prompt = `
    Identify key target audience segments for the following product:

    Product Name: ${productName}
    Industry: ${industry}
    Product Description: ${productDescription}

    For each audience segment, please provide:
    1. Segment name
    2. Demographics (age, gender, income, education, location, etc.)
    3. Psychographics (values, interests, attitudes, lifestyle)
    4. Behaviors (purchasing habits, brand loyalty, usage patterns)
    5. Pain points and goals
    6. Preferred communication channels

    Identify 3-5 distinct audience segments and format your response as a JSON array of segment objects.
    `;

    // Process with LLM
    const segmentsJson = await this.processRequest(prompt);

    try {
      // Parse the JSON response
      const segmentsData = JSON.parse(segmentsJson);

      // Create audience segment objects
      const audienceSegments: AudienceSegment[] = segmentsData.map((segment: any) => ({
        id: `segment-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
        name: segment.name || 'Unnamed Segment',
        demographics: segment.demographics || {},
        psychographics: segment.psychographics || [],
        behaviors: segment.behaviors || [],
        painPoints: segment.painPoints || [],
        goals: segment.goals || [],
        preferredChannels: segment.preferredChannels || [],
        createdAt: new Date()
      }));

      this.audienceSegments.push(...audienceSegments);

      // Store in memory for context
      if (!this.memory.Agent_Response.audienceSegments) {
        this.memory.Agent_Response.audienceSegments = [];
      }
      this.memory.Agent_Response.audienceSegments.push(...audienceSegments);

      return audienceSegments;
    } catch (error) {
      console.error('Error parsing audience segments:', error);
      throw new Error('Failed to parse audience segments response');
    }
  }

  /**
   * Analyze market trends
   */
  async analyzeMarketTrends(industry: string, timeframe: 'short-term' | 'medium-term' | 'long-term'): Promise<MarketTrend[]> {
    // Research current market trends
    const researchQuery = `latest market trends in ${industry} industry ${timeframe}`;
    const researchResults = await this.researchWeb(researchQuery);

    // Create a prompt for trend analysis
    const prompt = `
    Based on the following research about market trends in the ${industry} industry:

    ${researchResults}

    Please identify and analyze the top 3-5 ${timeframe} market trends that are most relevant.

    For each trend, provide:
    1. Trend name
    2. Brief description
    3. Potential impact (low, medium, high)
    4. Relevant industries
    5. Opportunities it presents
    6. Potential threats it poses

    Format your response as a JSON array of trend objects.
    `;

    // Process with LLM
    const trendsJson = await this.processRequest(prompt);

    try {
      // Parse the JSON response
      const trendsData = JSON.parse(trendsJson);

      // Create market trend objects
      const marketTrends: MarketTrend[] = trendsData.map((trend: any) => ({
        id: `trend-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
        name: trend.name || 'Unnamed Trend',
        description: trend.description || '',
        impact: trend.impact || 'medium',
        timeframe,
        relevantIndustries: trend.relevantIndustries || [industry],
        opportunities: trend.opportunities || [],
        threats: trend.threats || [],
        sources: trend.sources || [],
        createdAt: new Date()
      }));

      this.marketTrends.push(...marketTrends);

      // Store in memory for context
      if (!this.memory.Agent_Response.marketTrends) {
        this.memory.Agent_Response.marketTrends = [];
      }
      this.memory.Agent_Response.marketTrends.push(...marketTrends);

      return marketTrends;
    } catch (error) {
      console.error('Error parsing market trends:', error);
      throw new Error('Failed to parse market trends response');
    }
  }

  /**
   * Conduct comprehensive market research
   */
  async conductMarketResearch(productName: string, targetAudience: string, uniqueSellingPoints: string): Promise<string> {
    // Research product category
    const categoryResearch = await this.researchWeb(`${productName} market size industry trends`);

    // Research target audience
    const audienceResearch = await this.researchWeb(`${targetAudience} consumer behavior preferences`);

    // Research competitors
    const competitorResearch = await this.researchWeb(`top competitors for ${productName} market share`);

    // Get current date and time for the report
    const reportDateTime = dateTimeTool.getCurrentDateTime({
      format: 'full',
      includeTime: true,
      includeDayOfWeek: true
    });

    // Create a prompt for market research synthesis
    const prompt = `
    Synthesize the following market research information into a comprehensive market research report utilizing
    any combination of the following sections where applicable:

    Product Category Research:
    ${categoryResearch}

    Target Audience Research:
    ${audienceResearch}

    Competitor Research:
    ${competitorResearch}

    Product Unique Selling Points:
    ${uniqueSellingPoints}

    Please create a well-structured market research report that includes:
    1. Executive Summary
    2. Market Overview and Size
    3. Target Audience Analysis
    4. Competitive Landscape
    5. Market Opportunities
    6. Recommendations

    Include the following report date in the header: ${reportDateTime}

    Format the report in markdown.
    `;

    // Process with LLM
    const marketResearchReport = await this.processRequest(prompt);

    // Store in memory
    if (!this.memory.Agent_Response.marketResearchReports) {
      this.memory.Agent_Response.marketResearchReports = [];
    }

    this.memory.Agent_Response.marketResearchReports.push({
      productName,
      targetAudience,
      report: marketResearchReport,
      createdAt: new Date(),
      formattedTimestamp: dateTimeTool.getCurrentDateTime({
        format: 'full',
        includeTime: true
      })
    });

    return marketResearchReport;
  }

  /**
   * Generate audience insights chart
   */
  async generateAudienceInsightsChart(segmentIds: string[]): Promise<ChartGenerationResult> {
    // Get the selected audience segments
    const segments = this.audienceSegments.filter(segment => segmentIds.includes(segment.id));

    if (segments.length === 0) {
      throw new Error('No valid audience segments found');
    }

    // Prepare data for chart
    const chartData = segments.flatMap(segment => {
      return [
        { segment: segment.name, category: 'Age', value: segment.demographics.age || 'N/A' },
        { segment: segment.name, category: 'Income', value: segment.demographics.income || 'N/A' },
        { segment: segment.name, category: 'Education', value: segment.demographics.education || 'N/A' },
        { segment: segment.name, category: 'Location', value: segment.demographics.location || 'N/A' }
      ];
    });

    // Create a prompt for the audience insights chart
    const chartPrompt = `Generate a bar chart titled "Audience Segment Comparison" with the following data: ${JSON.stringify(chartData)}.
    The x-axis should represent the 'category' field, the y-axis should represent the 'value' field, and the series should be grouped by 'segment'.
    Use the following colors: #4CAF50, #2196F3, #FF9800, #F44336, #9C27B0.`;

    // Generate the chart using the prompt
    return await chartTool.generateChart({
      prompt: chartPrompt,
      chartType: 'bar'
    });
  }

  /**
   * Conduct marketing research on a specific topic or concept
   * This method allows the agent to handle research-based conceptual inquiries
   */
  async conductMarketingResearch(topic: string, context: string = '', researchQuestions: string[] = []): Promise<any> {
    // Get current date and time for the report
    const reportDateTime = dateTimeTool.getCurrentDateTime({
      format: 'full',
      includeTime: true,
      includeDayOfWeek: true
    });

    // Create a prompt for marketing research
    const prompt = `
    Conduct comprehensive marketing research on the following topic. Provide detailed analysis, insights, and strategic implications.

    Research Topic: ${topic}
    Context: ${context}

    ${researchQuestions.length > 0 ? `Specific Research Questions to Address:\n${researchQuestions.map((q, i) => `${i + 1}. ${q}`).join('\n')}` : ''}

    IMPORTANT: Include a section at the beginning that clearly indicates this response was generated by the Research & Insights Agent, delegated by the Strategic Director Agent.

    Please include the following report header information:
    - Marketing Research Report: ${topic}
    - Date: ${reportDateTime}
    - Prepared for: Strategic Director
    - Prepared by: Research & Insights Specialist

    Please provide a detailed research report that includes any combination of the following sections relevant to the topic:
    1. Executive Summary - Brief overview of key findings
    2. Research Methodology - Approach to analyzing this topic
    3. Market Analysis - Current state of the market related to this topic
    4. Consumer Insights - Understanding of consumer behavior and preferences
    5. Competitive Landscape - Analysis of key players and their strategies
    6. Trends and Opportunities - Emerging trends and potential opportunities
    7. Challenges and Threats - Potential obstacles and market threats
    8. Strategic Implications - How these findings impact marketing strategy
    9. Recommendations - Actionable recommendations based on research
    10. Conceptual Framework - Theoretical marketing concepts that apply to this topic
    11. References - Sources of information and data (if applicable from context)

    Format your response as a well-structured markdown document.
    `;

    // Process with LLM
    const researchReport = await this.processRequest(prompt);

    // Store in memory for context
    if (!this.memory.Agent_Response.marketingResearch) {
      this.memory.Agent_Response.marketingResearch = [];
    }

    const researchEntry = {
      id: `research-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
      topic,
      context,
      researchQuestions,
      report: researchReport,
      createdAt: new Date(),
      formattedTimestamp: reportDateTime
    };

    this.memory.Agent_Response.marketingResearch.push(researchEntry);

    return researchReport;
  }

  /**
   * Research and analyze a marketing topic using both document search and question answering
   * This method combines document search and LLM capabilities to provide comprehensive research
   *
   * @param topic - The marketing topic to research
   * @param researchQuestions - Optional specific questions to address
   * @param useInternetSearch - Whether to use internet search for additional information
   * @returns Comprehensive research results
   */
  async researchMarketingTopic(
    topic: string,
    researchQuestions: string[] = [],
    useInternetSearch: boolean = false
  ): Promise<any> {
    console.log(`ResearchInsightsAgent: Researching marketing topic "${topic}"`);

    try {
      // Step 1: Research the web for relevant information if enabled
      let webResearchResults = '';
      if (useInternetSearch) {
        webResearchResults = await this.researchWeb(`${topic} marketing research analysis`);
      }

      // Get current date and time for the report
      const reportDateTime = dateTimeTool.getCurrentDateTime({
        format: 'full',
        includeTime: true,
        includeDayOfWeek: true
      });

      // Step 2: Create a comprehensive research prompt
      const researchPrompt = `
      You are a marketing research specialist conducting research on the topic: "${topic}".

      ${researchQuestions.length > 0 ?
        `You need to answer the following specific research questions:
        ${researchQuestions.map((q, i) => `${i+1}. ${q}`).join('\n')}` :
        'You need to provide a comprehensive analysis of this topic.'}

      ${useInternetSearch && webResearchResults ? `
      WEB RESEARCH RESULTS:
      ${webResearchResults}
      ` : ''}

      IMPORTANT: Include a section at the beginning that clearly indicates this response was generated by the Research & Insights Agent, delegated by the Strategic Director Agent.

      Please include the following report header information:
      - Marketing Research Report: ${topic}
      - Date: ${reportDateTime}
      - Prepared for: Strategic Director
      - Prepared by: Research & Insights Specialist

      Please provide a detailed research report that includes:
      1. Executive Summary - Brief overview of key findings
      2. Background - Context and importance of the topic
      3. Methodology - How the research was conducted
      4. Key Findings - Main insights discovered
      5. Analysis - Interpretation of the findings
      6. Implications for Marketing Strategy - How these findings impact marketing approaches
      7. Recommendations - Actionable recommendations based on the research
      8. Conclusion - Summary of the research and next steps

      Your report should be well-structured, insightful, and directly address the topic and any specific questions.
      Format your response as a well-structured markdown document.
      `;

      // Process with LLM
      const analysisText = await this.processRequest(researchPrompt);

      // Step 3: Create a structured research report
      const report = {
        topic,
        webResearchResults: useInternetSearch ? {
          success: !!webResearchResults,
          content: webResearchResults
        } : null,
        analysis: {
          success: true,
          questions: researchQuestions.map(q => ({
            question: q,
            answer: "See comprehensive analysis in the report."
          })),
          summary: analysisText
        },
        recommendations: this.extractRecommendations(analysisText, researchQuestions.map(q => ({ question: q }))),
        timestamp: new Date(),
        formattedTimestamp: reportDateTime
      };

      // Store in memory for context
      if (!this.memory.Agent_Response.marketingResearch) {
        this.memory.Agent_Response.marketingResearch = [];
      }

      this.memory.Agent_Response.marketingResearch.push({
        id: `research-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
        topic,
        summary: analysisText,
        findings: report,
        timestamp: new Date(),
        formattedTimestamp: reportDateTime
      });

      return report;
    } catch (error) {
      console.error('Error researching marketing topic:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }

  /**
   * Extract recommendations from analysis text
   * Helper method to extract actionable recommendations from text
   *
   * @param summary - The text to extract recommendations from
   * @param questions - Optional array of questions with answers to also extract from
   * @returns Array of recommendation strings
   */
  private extractRecommendations(summary: string, questions: Array<{ question: string; answer?: string }>): string[] {
    // Simple extraction of sentences that might contain recommendations
    const recommendationIndicators = [
      'recommend', 'should', 'could', 'must', 'need to', 'important to',
      'consider', 'implement', 'adopt', 'focus on', 'prioritize', 'suggest'
    ];

    // Extract sentences from the summary
    const sentences = summary.split(/[.!?]+/).map(s => s.trim()).filter(s => s.length > 10);

    // Find sentences that might contain recommendations
    const potentialRecommendations = sentences.filter(sentence =>
      recommendationIndicators.some(indicator =>
        sentence.toLowerCase().includes(indicator)
      )
    );

    // Also look for recommendations in question answers
    const questionRecommendations: string[] = [];
    questions.forEach(qa => {
      if (qa.answer) {
        const qaSentences = qa.answer.split(/[.!?]+/).map((s: string) => s.trim()).filter((s: string) => s.length > 10);
        const qaRecs = qaSentences.filter((sentence: string) =>
          recommendationIndicators.some((indicator: string) =>
            sentence.toLowerCase().includes(indicator)
          )
        );
        questionRecommendations.push(...qaRecs);
      }
    });

    // Combine, deduplicate, and return
    const allRecommendations = [...potentialRecommendations, ...questionRecommendations];
    const uniqueRecommendations = [...new Set(allRecommendations)];

    return uniqueRecommendations.slice(0, 10); // Return at most 10 recommendations
  }

  /**
   * Get all competitive analyses
   */
  getCompetitiveAnalyses(): CompetitiveAnalysis[] {
    return this.competitiveAnalyses;
  }

  /**
   * Get a specific competitive analysis
   */
  getCompetitiveAnalysis(analysisId: string): CompetitiveAnalysis | undefined {
    return this.competitiveAnalyses.find(analysis => analysis.id === analysisId);
  }

  /**
   * Get all audience segments
   */
  getAudienceSegments(): AudienceSegment[] {
    return this.audienceSegments;
  }

  /**
   * Get a specific audience segment
   */
  getAudienceSegment(segmentId: string): AudienceSegment | undefined {
    return this.audienceSegments.find(segment => segment.id === segmentId);
  }

  /**
   * Get all market trends
   */
  getMarketTrends(): MarketTrend[] {
    return this.marketTrends;
  }

  /**
   * Get a specific market trend
   */
  getMarketTrend(trendId: string): MarketTrend | undefined {
    return this.marketTrends.find(trend => trend.id === trendId);
  }

  /**
   * Generate comprehensive research insights charts
   * @param researchContent - The research content to visualize
   * @param researchTopic - The research topic
   * @returns Array of chart generation results
   */
  async generateResearchCharts(
    researchContent: string,
    researchTopic: string,
    chartTypes: ('competitive' | 'market' | 'audience' | 'trends')[] = ['competitive', 'market', 'audience', 'trends']
  ): Promise<ChartGenerationResult[]> {
    const charts: ChartGenerationResult[] = [];

    try {
      console.log('ResearchInsightsAgent: Generating research insights charts');

      // 1. Generate Competitive Analysis Matrix (Table)
      if (chartTypes.includes('competitive')) {
        const competitivePrompt = `
Based on this research content, create a competitive analysis matrix table showing:
- Competitor Name
- Market Share %
- Strengths
- Weaknesses
- Pricing Strategy
- Unique Selling Points
- Threat Level

Research Content:
${researchContent.substring(0, 2000)}...

Research Topic: ${researchTopic}
`;

        const competitiveChart = await chartTool.generateChart({
          prompt: competitivePrompt,
          chartType: CHART_TYPES.TABLE,
          model: 'gpt-4o',
          provider: 'openai'
        });

        if (competitiveChart.success) {
          charts.push(competitiveChart);
          console.log('ResearchInsightsAgent: Generated competitive analysis matrix');
        }
      }

      // 2. Generate Market Trends Analysis (Flow Chart)
      if (chartTypes.includes('trends')) {
        const trendsPrompt = `
Based on this research content, create a market trends flow chart showing:
- Current market state
- Emerging trends
- Technology disruptions
- Consumer behavior shifts
- Future market direction
- Strategic implications

Research Content:
${researchContent.substring(0, 2000)}...

Research Topic: ${researchTopic}
`;

        const trendsChart = await chartTool.generateChart({
          prompt: trendsPrompt,
          chartType: CHART_TYPES.FLOW,
          model: 'gpt-4o',
          provider: 'openai'
        });

        if (trendsChart.success) {
          charts.push(trendsChart);
          console.log('ResearchInsightsAgent: Generated market trends flow chart');
        }
      }

      // 3. Generate Audience Segmentation Matrix (Table)
      if (chartTypes.includes('audience')) {
        const audiencePrompt = `
Based on this research content, create an audience segmentation matrix table showing:
- Segment Name
- Demographics
- Psychographics
- Behaviors
- Pain Points
- Preferred Channels
- Market Size %
- Engagement Strategy

Research Content:
${researchContent.substring(0, 2000)}...

Research Topic: ${researchTopic}
`;

        const audienceChart = await chartTool.generateChart({
          prompt: audiencePrompt,
          chartType: CHART_TYPES.TABLE,
          model: 'gpt-4o',
          provider: 'openai'
        });

        if (audienceChart.success) {
          charts.push(audienceChart);
          console.log('ResearchInsightsAgent: Generated audience segmentation matrix');
        }
      }

      // 4. Generate Market Opportunity Analysis (Table)
      if (chartTypes.includes('market')) {
        const marketPrompt = `
Based on this research content, create a market opportunity analysis table showing:
- Opportunity Area
- Market Size
- Growth Potential
- Competition Level
- Entry Barriers
- Investment Required
- ROI Potential
- Risk Level

Research Content:
${researchContent.substring(0, 2000)}...

Research Topic: ${researchTopic}
`;

        const marketChart = await chartTool.generateChart({
          prompt: marketPrompt,
          chartType: CHART_TYPES.TABLE,
          model: 'gpt-4o',
          provider: 'openai'
        });

        if (marketChart.success) {
          charts.push(marketChart);
          console.log('ResearchInsightsAgent: Generated market opportunity analysis');
        }
      }

      console.log(`ResearchInsightsAgent: Generated ${charts.length} research charts`);
      return charts;

    } catch (error) {
      console.error('ResearchInsightsAgent: Error generating research charts:', error);
      return charts; // Return any charts that were successfully generated
    }
  }

  /**
   * Research a topic and generate comprehensive insights with charts
   * @param topic - The research topic
   * @param researchQuestions - Optional specific questions
   * @param useInternetSearch - Whether to use internet search
   * @returns Research results with charts
   */
  async researchTopicWithCharts(
    topic: string,
    researchQuestions: string[] = [],
    useInternetSearch: boolean = false
  ): Promise<{
    research: any;
    charts: ChartGenerationResult[];
    success: boolean;
    error?: string;
  }> {
    try {
      // Generate the research content
      const research = await this.researchMarketingTopic(topic, researchQuestions, useInternetSearch);

      // Generate supporting charts
      const charts = await this.generateResearchCharts(
        research.analysis?.summary || '',
        topic
      );

      return {
        research,
        charts,
        success: true
      };
    } catch (error: any) {
      return {
        research: null,
        charts: [],
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Analyze competition for a product with specific competitors
   * @param productName - The product to analyze
   * @param competitors - List of competitors to analyze
   * @returns Competitive analysis object
   */
  async analyzeCompetition(productName: string, competitors: string[] = []): Promise<CompetitiveAnalysis> {
    console.log(`[ResearchInsightsAgent] Starting competition analysis for product: "${productName}" with competitors: ${competitors.join(', ')}`);

    try {
      // Create a prompt for competitive analysis with specific competitors
      const competitorsList = competitors.length > 0 ? competitors.join(', ') : 'identify top competitors';
      const prompt = `
      Conduct a comprehensive competitive analysis for the following product:

      Product Name: ${productName}
      ${competitors.length > 0 ? `Specific Competitors to Analyze: ${competitorsList}` : 'Please identify the top 3-5 competitors in this space'}

      Please provide:
      1. Analysis of each competitor's strengths and weaknesses
      2. Their unique selling points
      3. Market gaps that could be exploited
      4. Opportunities and threats in the competitive landscape

      IMPORTANT: Format your response as a valid JSON object with these sections as keys.
      DO NOT include any markdown formatting, code blocks, or explanatory text.
      ONLY return the raw JSON object.

      The expected format is:
      {
        "competitors": [
          {
            "name": "Competitor Name",
            "strengths": ["Strength 1", "Strength 2"],
            "weaknesses": ["Weakness 1", "Weakness 2"],
            "uniqueSellingPoints": ["USP 1", "USP 2"]
          }
        ],
        "marketGaps": ["Gap 1", "Gap 2"],
        "opportunities": ["Opportunity 1", "Opportunity 2"],
        "threats": ["Threat 1", "Threat 2"]
      }
      `;

      // Process with LLM
      console.log(`[ResearchInsightsAgent] Sending competition analysis prompt to LLM`);
      const analysisJson = await this.processRequest(prompt);
      console.log(`[ResearchInsightsAgent] Received response from LLM, attempting to parse JSON`);

      // Extract JSON from the response
      const cleanJson = this.extractJsonFromText(analysisJson);
      console.log(`[ResearchInsightsAgent] Attempting to parse cleaned JSON`);

      // Parse the JSON response
      const parsedJson = JSON.parse(cleanJson);

      // Validate with Zod schema
      console.log(`[ResearchInsightsAgent] Validating JSON with Zod schema`);
      const validationResult = CompetitiveAnalysisSchema.safeParse(parsedJson);

      if (!validationResult.success) {
        console.error(`[ResearchInsightsAgent] Zod validation failed:`, validationResult.error);
        throw new Error(`Invalid competitive analysis data: ${validationResult.error.message}`);
      }

      // Extract the validated data
      const analysisData = validationResult.data;
      console.log(`[ResearchInsightsAgent] Successfully validated JSON data`);

      // Create the competitive analysis object
      const competitiveAnalysis: CompetitiveAnalysis = {
        id: `comp-analysis-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
        productName,
        competitors: analysisData.competitors,
        marketGaps: analysisData.marketGaps,
        opportunities: analysisData.opportunities,
        threats: analysisData.threats,
        createdAt: new Date(),
        formattedTimestamp: dateTimeTool.getCurrentDateTime({
          format: 'medium',
          includeTime: true
        })
      };

      console.log(`[ResearchInsightsAgent] Created competitive analysis object with ${competitiveAnalysis.competitors.length} competitors`);
      this.competitiveAnalyses.push(competitiveAnalysis);

      // Store in memory for context
      if (!this.memory.Agent_Response.competitiveAnalyses) {
        this.memory.Agent_Response.competitiveAnalyses = [];
      }
      this.memory.Agent_Response.competitiveAnalyses.push(competitiveAnalysis);

      return competitiveAnalysis;
    } catch (error) {
      console.error('[ResearchInsightsAgent] Error in analyzeCompetition:', error);
      throw new Error(error instanceof Error ? error.message : 'Unknown error occurred during competitive analysis');
    }
  }

  /**
   * Generate competitive analysis with charts
   * @param productName - The product to analyze
   * @param competitors - List of competitors
   * @returns Competitive analysis with charts
   */
  async analyzeCompetitionWithCharts(
    productName: string,
    competitors: string[] = []
  ): Promise<{
    analysis: CompetitiveAnalysis;
    charts: ChartGenerationResult[];
    success: boolean;
    error?: string;
  }> {
    try {
      // Generate the competitive analysis
      const analysis = await this.analyzeCompetition(productName, competitors);

      // Create content for chart generation
      const analysisContent = `
Product: ${productName}
Competitors: ${analysis.competitors.map(c => `${c.name}: ${c.strengths.join(', ')}`).join('; ')}
Market Gaps: ${analysis.marketGaps.join(', ')}
Opportunities: ${analysis.opportunities.join(', ')}
Threats: ${analysis.threats.join(', ')}
`;

      // Generate supporting charts
      const charts = await this.generateResearchCharts(
        analysisContent,
        `Competitive Analysis: ${productName}`,
        ['competitive', 'market']
      );

      return {
        analysis,
        charts,
        success: true
      };
    } catch (error: any) {
      return {
        analysis: {} as CompetitiveAnalysis,
        charts: [],
        success: false,
        error: error.message
      };
    }
  }
}




