/**
 * Strategic Director Agent
 *
 * This agent is the foundation and coordinates all strategic marketing activities:
 * - Analyzes products' unique value propositions
 * - Develops comprehensive marketing strategies
 * - Coordinates workflow between other agents
 * - Ensures alignment between research insights and content creation
 * - Makes strategic adjustments based on performance data
 */

import { MarketingAgent } from './MarketingAgent';
import type { AgentMessage } from './MarketingAgent';
import { LlmProvider } from '../../tools/llm-tool';
import { ChartGenerationResult as BaseChartGenerationResult } from '../../tools/chart-tool';
// Import the new QueryDocumentsAgent and its result type
import { QueryDocumentsAgent, QueryDocumentsAgentResult as BaseQueryDocumentsAgentResult } from '../../../components/Agents/QueryDocumentsAgent'; // Adjust path as needed
import { QuestionAnswerAgent, QuestionAnswerResult } from '../../../components/Agents/QuestionAnswerAgent';
import { createResearchInsightsTool } from '../../tools/researchInsightsTool';
import { dateTimeTool } from '../../tools/dateTimeTool';
import { AgenticTeamId } from '../pmo/PMOInterfaces';
import { Task, TaskStatus, TaskPriority } from '../../../admin/planner/types';

// Import abstracted utility modules
import { StrategicTaskMapper } from '../../utils/strategic-task-mapper';
import { StrategicTaskFormatter } from '../../utils/strategic-task-formatter';
import { StrategicTaskConverter } from '../../utils/strategic-task-converter';
import { StrategicTaskUtils } from '../../utils/strategic-task-utils';
import { StrategicChartGenerator } from '../../utils/strategic-chart-generator';


// Extended chart generation result with fallback description
interface ChartGenerationResult extends BaseChartGenerationResult {
  fallbackDescription?: string;
}

// Define types for chart handling
type ChartConfigType = any; // Using any for now, but ideally this would match the actual chart config structure
import { PdfContent, PdfGenerationOptions } from '../../tools/pdf-generator';
import { SavePdfToByteStoreResult } from '../../tools/storage-tool';
import { z } from 'zod';

// Use the QueryDocumentsAgentResult type from the imported agent
export type QueryDocumentsAgentResult = BaseQueryDocumentsAgentResult;

/**
 * Interface for structured information gap identification
 */
interface InformationGap {
  category: string;
  description: string;
  priority: 'HIGH' | 'MEDIUM' | 'LOW';
  assignedTeam: string;
  specificRequirements: string[];
  deliverable: string;
  timeline: string;
  dependencies?: string[];
}

interface InformationGapAnalysis {
  hasGaps: boolean;
  identifiedGaps: InformationGap[];
  summary: string;
  totalGaps: number;
}

/**
 * Comprehensive Task Structure for ALL Strategic Director Operations
 */
interface StrategicTask {
  id: string;
  title: string;
  description: string;
  category: 'Market Intelligence' | 'Product Analysis' | 'Customer Intelligence' | 'Marketing Infrastructure' | 'Performance Metrics' | 'Customer Validation' | 'Strategic Planning' | 'Implementation' | 'Research' | 'Content Creation';
  priority: 'CRITICAL' | 'HIGH' | 'MEDIUM' | 'LOW';
  assignedTeam: 'Research Team' | 'Business Analysis Team' | 'Marketing Team' | 'Sales Team' | 'Software Design Team' | 'Content Team' | 'Strategic Director';
  status: 'IDENTIFIED' | 'ASSIGNED' | 'IN_PROGRESS' | 'REVIEW' | 'COMPLETED' | 'BLOCKED' | 'CANCELLED';
  specificRequirements: string[];
  deliverable: string;
  timeline: {
    estimatedDuration: string;
    startDate?: Date;
    dueDate?: Date;
    milestones?: Array<{
      name: string;
      date: Date;
      description: string;
      completed: boolean;
    }>;
  };
  dependencies?: string[]; // Task IDs this task depends on
  blockers?: string[]; // Current blockers preventing progress
  successCriteria: string[];
  resources?: {
    budget?: number;
    tools?: string[];
    personnel?: string[];
    documents?: string[];
  };
  metadata: {
    createdAt: Date;
    createdBy: string;
    updatedAt: Date;
    source: 'Information Gap Analysis' | 'Strategic Planning' | 'PMO Requirements' | 'User Request' | 'System Generated';
    pmoId?: string;
    projectId?: string;
    requestId?: string;
  };
}

interface StrategicTaskCollection {
  collectionId: string;
  name: string;
  description: string;
  source: 'PMO Project' | 'Strategic Analysis' | 'Information Gap Resolution' | 'User Request';
  totalTasks: number;
  tasks: StrategicTask[];
  overallTimeline: {
    startDate: Date;
    estimatedEndDate: Date;
    criticalPath: string[]; // Task IDs on critical path
  };
  teamAssignments: {
    [teamName: string]: {
      taskCount: number;
      taskIds: string[];
      estimatedWorkload: string;
    };
  };
  successMetrics: string[];
  createdAt: Date;
  updatedAt: Date;
}

interface TaskGenerationContext {
  userRequest: string;
  analysisContent: string;
  pmoContext?: {
    pmoId: string;
    projectTitle: string;
    priority: string;
    category: string;
  };
  identifiedGaps?: InformationGap[];
  strategicObjectives?: string[];
  constraints?: string[];
}


/**
 * Zod schema for SWOT Analysis
 */
const SwotAnalysisSchema = z.object({
  strengths: z.array(z.string()).default([]),
  weaknesses: z.array(z.string()).default([]),
  opportunities: z.array(z.string()).default([]),
  threats: z.array(z.string()).default([]),
  description: z.string().optional().default("") // Overall description of the SWOT analysis
});

/**
 * Zod schema for Product Analysis Response
 */
const ProductAnalysisResponseSchema = z.object({
  uniqueSellingPoints: z.array(z.string()).default([]),
  competitiveAdvantages: z.array(z.string()).default([]),
  targetMarket: z.array(z.string()).default([]),
  pricingStrategy: z.string().default(""),
  distributionChannels: z.array(z.string()).default([]),
  marketPosition: z.string().optional().default(""), // Market positioning description
  industryTrends: z.string().optional().default(""), // Relevant industry trends
  customerInsights: z.string().optional().default(""), // Detailed customer insights
  swotAnalysis: SwotAnalysisSchema.default({
    strengths: [],
    weaknesses: [],
    opportunities: [],
    threats: [],
    description: ""
  }),
  recommendations: z.array(z.string()).optional().default([]), // Strategic recommendations
  researchNotes: z.string().optional().default("") // Additional research notes
});

/**
 * Zod schema for Target Audience
 * COMMENTED OUT TO REDUCE COMPLEXITY
 */
/*
const TargetAudienceSchema = z.object({
  demographics: z.array(z.string()).default([]),
  psychographics: z.array(z.string()).default([]),
  behaviors: z.array(z.string()).default([]),
  description: z.string().optional().default(""), // Detailed audience description
  painPoints: z.array(z.string()).optional().default([]), // Customer pain points
  buyingJourney: z.string().optional().default(""), // Description of the buying journey
  segmentDetails: z.array(z.object({
    name: z.string(),
    description: z.string(),
    characteristics: z.array(z.string()).default([])
  })).optional().default([]) // More detailed audience segments
});
*/

/**
 * Zod schema for Timeline Milestone
 * COMMENTED OUT TO REDUCE COMPLEXITY
 */
/*
const TimelineMilestoneSchema = z.object({
  name: z.string(),
  date: z.string(), // Will be converted to Date later
  description: z.string().default("")
});
*/

/**
 * Zod schema for Marketing Strategy Response
 * COMMENTED OUT TO REDUCE COMPLEXITY
 */
/*
const MarketingStrategyResponseSchema = z.object({
  name: z.string().default(""),
  description: z.string().default(""),
  executiveSummary: z.string().optional().default(""), // High-level summary
  objectives: z.array(z.string()).default([]),
  targetAudience: TargetAudienceSchema.default({
    demographics: [],
    psychographics: [],
    behaviors: [],
    description: ""
    // segmentDetails and other optional fields will use their defaults if not provided
  }),
  valueProposition: z.string().default(""),
  keyMessages: z.array(z.string()).default([]),
  channels: z.array(z.string()).default([]),
  channelStrategy: z.record(z.string(), z.string()).optional().default({}), // Detailed strategy per channel
  contentStrategy: z.string().optional().default(""), // Content strategy description
  timeline: z.object({
    milestones: z.array(TimelineMilestoneSchema).default([]),
    description: z.string().optional().default("") // Timeline description
  }).default({ milestones: [], description: "" }),
  budget: z.object({
    total: z.number().optional(),
    breakdown: z.record(z.string(), z.number()).optional(),
    notes: z.string().optional()
  }).optional().default({}),
  kpis: z.array(z.string()).default([]),
  riskAssessment: z.array(z.object({
    risk: z.string(),
    impact: z.string(),
    mitigation: z.string()
  })).optional().default([]),
  competitiveAnalysis: z.string().optional().default(""), // Competitive analysis
  marketingResearch: z.string().optional().default(""), // Additional research findings
  conceptualFramework: z.string().optional().default("") // Theoretical/conceptual marketing framework
});
*/

export interface MarketingStrategy {
  id: string;
  name: string;
  description: string;
  executiveSummary?: string;
  objectives: string[];
  targetAudience: {
    demographics: string[];
    psychographics: string[];
    behaviors: string[];
    description?: string;
    painPoints?: string[];
    buyingJourney?: string;
    segmentDetails?: Array<{
      name: string;
      description: string;
      characteristics: string[];
    }>;
  };
  valueProposition: string;
  keyMessages: string[];
  channels: string[];
  channelStrategy?: Record<string, string>; // Detailed strategy per channel
  contentStrategy?: string;
  timeline: {
    startDate: Date;
    endDate: Date;
    description?: string;
    milestones: Array<{
      name: string;
      date: Date;
      description: string;
    }>;
  };
  budget?: {
    total?: number;
    breakdown?: Record<string, number>;
    notes?: string;
  };
  kpis: string[];
  riskAssessment?: Array<{
    risk: string;
    impact: string;
    mitigation: string;
  }>;
  competitiveAnalysis?: string;
  marketingResearch?: string;
  conceptualFramework?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface ProductAnalysis {
  id: string;
  name: string;
  description: string;
  uniqueSellingPoints: string[];
  competitiveAdvantages: string[];
  targetMarket: string[];
  pricingStrategy: string;
  distributionChannels: string[];
  marketPosition?: string;
  industryTrends?: string;
  customerInsights?: string;
  swotAnalysis: {
    strengths: string[];
    weaknesses: string[];
    opportunities: string[];
    threats: string[];
    description?: string;
  };
  recommendations?: string[];
  researchNotes?: string;
  createdAt: Date;
}

/**
 * Helper function to extract and repair JSON from a potentially markdown-formatted string
 * @param text - The text that might contain JSON wrapped in markdown code blocks
 * @returns The extracted and repaired JSON string
 */
function extractJsonFromText(text: string): string {
  // Check if the text contains markdown code blocks
  const jsonRegex = /```(?:json)?\s*([\s\S]*?)```/;
  const match = text.match(jsonRegex);

  let jsonText = '';

  if (match && match[1]) {
    // Extract content inside the code block
    jsonText = match[1].trim();
  } else {
    // Check if the text starts with natural language and then contains JSON
    // This handles cases where the LLM starts with "Certainly!" or similar phrases
    const jsonStartIndex = text.indexOf('{');
    const jsonEndIndex = text.lastIndexOf('}');

    if (jsonStartIndex !== -1 && jsonEndIndex !== -1 && jsonEndIndex > jsonStartIndex) {
      jsonText = text.substring(jsonStartIndex, jsonEndIndex + 1);
    } else {
      // If no JSON-like structure found, return the original text
      return text.trim();
    }
  }

  // Try to repair common JSON issues
  try {
    // Test if the JSON is already valid
    JSON.parse(jsonText);
    return jsonText;
  } catch (error) {
    console.log('Attempting to repair malformed JSON...');

    // 1. Fix trailing commas in objects and arrays
    jsonText = jsonText.replace(/,\s*(\}|\])/g, '$1');

    // 2. Fix missing commas between properties
    jsonText = jsonText.replace(/("[^"]+"\s*:\s*"[^"]*"|"[^"]+"\s*:\s*\{[^\}]*\}|"[^"]+"\s*:\s*\[[^\]]*\]|"[^"]+"\s*:\s*[\d\.]+|"[^"]+"\s*:\s*true|"[^"]+"\s*:\s*false|"[^"]+"\s*:\s*null)\s*(")/g, '$1,$2');

    // 3. Fix unquoted property names
    jsonText = jsonText.replace(/(\{|,)\s*([a-zA-Z0-9_]+)\s*:/g, '$1"$2":');

    // 4. Fix single quotes used instead of double quotes
    jsonText = jsonText.replace(/'/g, '"');

    // 5. Fix missing quotes around string values
    // This is tricky and might cause issues, so we're careful
    jsonText = jsonText.replace(/:\s*([a-zA-Z][a-zA-Z0-9_\s]+)([,\}])/g, ':"$1"$2');

    // 6. Remove any non-standard characters that might have been introduced
    jsonText = jsonText.replace(/[\u0000-\u001F\u007F-\u009F]/g, '');

    // 7. Try to balance brackets and braces if needed
    const openBraces = (jsonText.match(/\{/g) || []).length;
    const closeBraces = (jsonText.match(/\}/g) || []).length;
    const openBrackets = (jsonText.match(/\[/g) || []).length;
    const closeBrackets = (jsonText.match(/\]/g) || []).length;

    if (openBraces > closeBraces) {
      jsonText += '}'.repeat(openBraces - closeBraces);
    }

    if (openBrackets > closeBrackets) {
      jsonText += ']'.repeat(openBrackets - closeBrackets);
    }

    try {
      // Test if our repairs fixed the JSON
      JSON.parse(jsonText);
      console.log('JSON repair successful');
      return jsonText;
    } catch (repairError) {
      console.error('JSON repair failed:', repairError);

      // Last resort: Try to extract just the valid parts of the JSON
      // This is a simplified approach - in a real system, you might want a more sophisticated JSON repair library
      try {
        // Try to extract and parse each top-level property individually
        const propertyRegex = /"([^"]+)"\s*:\s*(\{[^\}]*\}|\[[^\]]*\]|"[^"]*"|[\d\.]+|true|false|null)/g;
        let propMatch; // Renamed to avoid conflict with outer 'match'
        const properties: Record<string, any> = {};

        while ((propMatch = propertyRegex.exec(jsonText)) !== null) {
          try {
            const propName = propMatch[1];
            const propValue = propMatch[2];

            // Try to parse the property value
            if (propValue.startsWith('{') || propValue.startsWith('[')) {
              try {
                properties[propName] = JSON.parse(propValue);
              } catch {
                // If parsing fails, store as string
                properties[propName] = propValue;
              }
            } else if (propValue === 'true') {
              properties[propName] = true;
            } else if (propValue === 'false') {
              properties[propName] = false;
            } else if (propValue === 'null') {
              properties[propName] = null;
            } else if (propValue.startsWith('"')) {
              // String value
              properties[propName] = propValue.slice(1, -1);
            } else {
              // Number value
              properties[propName] = Number(propValue);
            }
          } catch (propError) {
            console.error('Error parsing property:', propMatch[0]);
          }
        }

        if (Object.keys(properties).length > 0) {
          console.log('Partial JSON extraction successful');
          return JSON.stringify(properties);
        }
      } catch (extractError) {
        console.error('Partial JSON extraction failed:', extractError);
      }

      // If all else fails, return a minimal valid JSON object
      console.log('Returning minimal valid JSON as fallback');
      return '{}';
    }
  }
}

/**
 * Parse and validate LLM response for product analysis
 * @param response - Raw LLM response
 * @returns Validated product analysis data
 */
function parseProductAnalysisResponse(response: string): z.infer<typeof ProductAnalysisResponseSchema> {
  try {
    // Extract JSON from potentially markdown-formatted response
    const extractedJson = extractJsonFromText(response);
    console.log('Extracted JSON from product analysis:', extractedJson.substring(0, 100) + '...');

    try {
      // Try to parse the JSON and validate with Zod schema
      const parsedData = JSON.parse(extractedJson);
      const result = ProductAnalysisResponseSchema.safeParse(parsedData);

      if (result.success) {
        return result.data;
      } else {
        console.error('Product analysis validation errors:', result.error.format());
        // Instead of returning empty defaults, try to extract partial data
        return extractPartialData(parsedData, ProductAnalysisResponseSchema);
      }
    } catch (jsonError) {
      console.error('JSON parse error:', jsonError);
      // If JSON parsing fails, try to extract structured data directly with Zod
      return extractStructuredData(response, ProductAnalysisResponseSchema);
    }
  } catch (error) {
    console.error('Error parsing product analysis response:', error);
    // Return default values from the schema as last resort
    return ProductAnalysisResponseSchema.parse({});
  }
}

/**
 * Helper function to extract partial data from a partially valid object using a Zod schema
 * @param data - The data object that might be partially valid
 * @param schema - The Zod schema to validate against
 * @returns A validated object with as much data as could be extracted
 */
function extractPartialData<T extends z.ZodTypeAny>(data: any, schema: T): z.infer<T> {
  // Create a result object with schema defaults
  const result = schema.parse({});

  // Try to extract each top-level property individually
  if (data && typeof data === 'object') {
    // Get the shape of the schema
    const schemaShape = schema instanceof z.ZodObject ? schema.shape : {};

    // Process each property in the data
    for (const [key, value] of Object.entries(data)) {
      try {
        // Check if this property exists in the schema
        if (key in schemaShape) {
          const propertySchema = (schemaShape as any)[key];

          // Try to validate this property
          const validatedValue = propertySchema.safeParse(value);
          if (validatedValue.success) {
            (result as any)[key] = validatedValue.data;
          } else if (value && typeof value === 'object') {
            // For nested objects, try to extract partial data recursively
            if (propertySchema instanceof z.ZodObject) {
              (result as any)[key] = extractPartialData(value, propertySchema);
            } else if (propertySchema instanceof z.ZodArray && Array.isArray(value)) {
              // For arrays, try to validate each item
              const arraySchema = propertySchema.element;
              (result as any)[key] = value
                .map((item: any) => { // Added type for item
                  try {
                    const itemResult = arraySchema.safeParse(item);
                    return itemResult.success ? itemResult.data : null;
                  } catch {
                    return null;
                  }
                })
                .filter(item => item !== null);
            }
          }
        }
      } catch (error) {
        console.error(`Error extracting property ${key}:`, error);
      }
    }
  }

  return result;
}

/**
 * Helper function to normalize schema keys or section titles for matching.
 * Converts to lowercase and removes non-alphanumeric characters.
 * @param key - The string to normalize
 * @returns Normalized string
 */
function normalizeKeyForMatching(key: string): string {
  return key.toLowerCase().replace(/[^a-z0-9]/gi, '');
}


/**
 * Helper function to extract structured data directly from text using regex patterns
 * @param text - The text to extract data from
 * @param schema - The Zod schema to validate against
 * @returns A validated object with as much data as could be extracted
 */
function extractStructuredData<T extends z.ZodTypeAny>(text: string, schema: T): z.infer<T> {
  // Create a result object with schema defaults
  const result = schema.parse({});

  // Get the shape of the schema
  const schemaShape = schema instanceof z.ZodObject ? schema.shape : {};

  // Define regex patterns for different data types
  const patterns = {
    // Match section titles and their content
    section: /\b([A-Z][A-Za-z\s()]+):\s*([\s\S]*?)(?=\b[A-Z][A-Za-z\s()]+:|$)/g, // Added () to section title regex
    // Match bullet points
    bulletPoints: /[-•*]\s*([^\n]+)/g,
    // Match key-value pairs
    keyValue: /([A-Za-z\s]+):\s*([^\n]+)/g
  };

  // Extract sections
  let sectionMatch;
  while ((sectionMatch = patterns.section.exec(text)) !== null) {
    const sectionTitle = sectionMatch[1].trim();
    const sectionContent = sectionMatch[2].trim();

    const normalizedSectionTitleFromText = normalizeKeyForMatching(sectionTitle);

    try {
      let matchedSchemaKey: string | null = null;
      // Find the corresponding schema key from the Zod schema's shape
      for (const keyInSchema of Object.keys(schemaShape)) {
        if (normalizeKeyForMatching(keyInSchema) === normalizedSectionTitleFromText) {
          matchedSchemaKey = keyInSchema; // Store the original schema key
          break;
        }
      }

      if (matchedSchemaKey) {
        const propertyName = matchedSchemaKey; // Use the actual schema key for assignment
        const propertySchema = (schemaShape as any)[propertyName];

        // Handle different types of properties
        if (propertySchema instanceof z.ZodArray) {
          // Extract bullet points as array items
          const items: string[] = [];
          let bulletMatch;
          while ((bulletMatch = patterns.bulletPoints.exec(sectionContent)) !== null) {
            items.push(bulletMatch[1].trim());
          }

          // If no bullet points found, try to split by newlines
          if (items.length === 0) {
            items.push(...sectionContent.split('\n').map(line => line.trim()).filter(line => line));
          }

          // Validate array items
          const validatedItems = items
            .map(item => {
              try {
                // For array of objects, this parsing might be too simple.
                // Assuming array of simple strings for now as per schema defaults.
                const itemResult = propertySchema.element.safeParse(item);
                return itemResult.success ? itemResult.data : null;
              } catch {
                return null;
              }
            })
            .filter(item => item !== null);

          if (validatedItems.length > 0) {
            (result as any)[propertyName] = validatedItems;
          }
        } else if (propertySchema instanceof z.ZodString) {
          // For string properties, use the section content directly
          (result as any)[propertyName] = sectionContent;
        } else if (propertySchema instanceof z.ZodObject) {
          // For object properties, try to extract key-value pairs
          const nestedObject: Record<string, string> = {};
          let kvMatch;
          while ((kvMatch = patterns.keyValue.exec(sectionContent)) !== null) {
            const key = kvMatch[1].trim();
            const value = kvMatch[2].trim();
            nestedObject[normalizeKeyForMatching(key)] = value; // Normalize key for nested object
          }

          // Attempt to parse nestedObject against the propertySchema
          // This part might need more sophisticated parsing if the nested object structure is complex
          // For now, we directly try to parse what we got.
          try {
            const validatedObject = propertySchema.safeParse(nestedObject);
            if (validatedObject.success) {
              (result as any)[propertyName] = validatedObject.data;
            } else {
                 // If direct parsing fails, try to use extractPartialData for the nested object
                (result as any)[propertyName] = extractPartialData(nestedObject, propertySchema);
            }
          } catch {
            // Fallback: assign as is if schema parsing fails, or handle error
            // For simplicity, if parsing fails, the default from schema.parse({}) remains.
             console.warn(`Could not fully parse nested object for ${propertyName} from text. Partial data may be missing.`);
          }
        }
      }
    } catch (error) {
      console.error(`Error extracting section ${sectionTitle}:`, error);
    }
  }

  return result;
}


/**
 * Parse and validate LLM response for marketing strategy
 * @param response - Raw LLM response
 * @returns Validated marketing strategy data
 * COMMENTED OUT TO REDUCE COMPLEXITY
 */
/*
function parseMarketingStrategyResponse(response: string): z.infer<typeof MarketingStrategyResponseSchema> {
  try {
    // Extract JSON from potentially markdown-formatted response
    const extractedJson = extractJsonFromText(response);
    console.log('Extracted JSON from marketing strategy:', extractedJson.substring(0, 100) + '...');

    try {
      // Try to parse the JSON and validate with Zod schema
      const parsedData = JSON.parse(extractedJson);
      const result = MarketingStrategyResponseSchema.safeParse(parsedData);

      if (result.success) {
        return result.data;
      } else {
        console.error('Marketing strategy validation errors:', result.error.format());
        // Instead of returning empty defaults, try to extract partial data
        return extractPartialData(parsedData, MarketingStrategyResponseSchema);
      }
    } catch (jsonError) {
      console.error('JSON parse error:', jsonError);
      // If JSON parsing fails, try to extract structured data directly with Zod
      return extractStructuredData(response, MarketingStrategyResponseSchema);
    }
  } catch (error) {
    console.error('Error parsing marketing strategy response:', error);
    // Return default values from the schema as last resort
    return MarketingStrategyResponseSchema.parse({});
  }
}
*/

export class StrategicDirectorAgent extends MarketingAgent {
  private strategies: MarketingStrategy[] = [];
  private productAnalyses: ProductAnalysis[] = [];
  private chartFallbacks: Record<string, string> = {};
  private queryDocumentsAgent: QueryDocumentsAgent; // Instance of the new agent
  private questionAnswerAgent: QuestionAnswerAgent; // Instance for information sufficiency assessment

  constructor(
    id: string = 'strategic-director',
    name: string = 'Strategic Director',
    userId: string = '',
    defaultLlmProvider: LlmProvider = 'openai',
    defaultLlmModel: string = 'gpt-4o',
    _config: {
      useMemory?: boolean;
      maxQuestions?: number;
      enabledTools?: {
        internetSearch?: boolean;
        calculator?: boolean;
        calendar?: boolean;
      };
    } = {}
  ) {
    const role = 'Strategic Director';
    const description = `As the Strategic Director, I am responsible for developing comprehensive marketing strategies,
analyzing product value propositions, coordinating the marketing team workflow, and ensuring all marketing
activities align with business objectives. I make data-driven decisions to optimize marketing performance
and adjust strategies as needed. I point out if there is not enough informaion to answer the user's request and
ask for more information to be provided. I will instruct the user as to specifically what information is needed,
and will not proceed any further until the information is provided.`;

    super(id, name, role, description, userId, defaultLlmProvider, defaultLlmModel);

    // Initialize the new QueryDocumentsAgent
    this.queryDocumentsAgent = new QueryDocumentsAgent({
        maxResults: 2, // Example configuration, adjust as needed
        defaultTemperature: 0.3,
        defaultMaxTokens: 3000
    });

    // Initialize the QuestionAnswerAgent for information sufficiency assessment
    this.questionAnswerAgent = new QuestionAnswerAgent();
  }

  /**
   * Override the base getThinking method to provide more strategic analysis and reasoning
   * This specialized version focuses on documenting the strategic director's analysis process
   * and coordination with other team members
   * @param request The user request
   * @returns A detailed explanation of the strategic thinking process
   */
  async getThinking(request: string, context?: string): Promise<string> {
    try {
      // Create a prompt that asks the agent to explain its strategic thinking process
      const prompt = `You are ${this.name}, the Strategic Director responsible for developing comprehensive marketing strategies.
${this.description}

CONTEXT INFORMATION:
${context ? `The following context has been provided or retrieved:
${context}

Respond to the ${request} by reviewing the context
If no specific context has been provided inform the user as to what you require to complete their request.` : 'No specific context has been provided, so you will analyze the user\'s request and determine if there is enough information to proceed.'}

Please provide a detailed explanation that includes any of the following where its relevant:

1. STRATEGIC ANALYSIS:
   - How you understand and interpret this request in light of the provided context
   - The key marketing objectives that should be addressed
   - The strategic frameworks and models you would apply (e.g., SWOT, Porter's Five Forces, etc.)
   - How you would analyze the market, competition, and target audience

2. STRATEGIC PLANNING:
   - Your approach to developing a comprehensive marketing strategy
   - How you would structure the strategic plan
   - Key performance indicators (KPIs) you would establish
   - Resource allocation considerations

3. TEAM COORDINATION:
   - How you would coordinate with other team members (Research Insights, Content Creator, Social Media Orchestrator, Analytics)
   - What specific tasks you would delegate to each team member
   - How you would ensure alignment between different marketing functions
   - How you would integrate insights from each team member into the overall strategy

4. IMPLEMENTATION OVERSIGHT:
   - How you would monitor strategy implementation
   - Your approach to measuring effectiveness
   - How you would handle potential challenges or adjustments

5. MARKETING CONSISTENCY
  - Where it's available, adhere to the brand identity, brand guidelines, personality and brand voice from the context provided
  - If this is to be established engage your team to develop the clients identity for consistency
  - Elicit and document the brand identity, brand guidelines, personality and brand voice in a section titled "Brand Identity"
  - Be midful of Audience/Customer Personas as inferred from the context provided and engage your team to develop customer personas for consistency
  - Elicit and document the customer personas in a section titled "Customer Personas" which will be based on  demographics,
  motivatons, interests, pain points, and online behaviors

  6. SEO Requirements:
  - Engage your team to establish what keywords should your client target?
  - How should content be structured for search visibility?

  7. INFORMATION REQUIREMENTS:
   - Assess if you have enough information to proceed with a comprehensive response
   - If not, provide a detailed list of specific information needed from the user
   - For each information requirement, explain why it's necessary and how it will impact your strategic approach
   - Provide clear, actionable instructions for what the user should provide to help you deliver the best possible strategic analysis

   Document your reasoning in detail, showing your expertise in strategic marketing leadership and cross-functional coordination.
`;

      // Create model options based on the provider
      const modelOptions: any = { temperature: 0.7 };

      // Process with LLM
      const thinking = await this.llmTool.processContent({
        prompt,
        model: this.defaultLlmModel,
        provider: this.defaultLlmProvider,
        modelOptions
      });

      return thinking;
    } catch (error) {
      console.error(`Error getting strategic thinking process in ${this.name}:`, error);
      return `I encountered an error while documenting my strategic thinking process. The error was: ${error instanceof Error ? error.message : String(error)}`;
    }
  }

  /**
   * Override the base processRequest method to always include strategic reasoning
   * and generate a PDF document with analysis. This method is intended for direct user interactions.
   * @param request The user request
   * @param context Optional context for the request
   * @param category Optional category for document queries (defaults to 'strategic-analysis')
   * @returns The response with strategic reasoning included
   */
  async processRequest(request: string, context?: string, category?: string): Promise<string> {
    try {
      // First, detect if this is a research-related request
      const isResearchRequest = await this.detectResearchRequest(request);

      if (isResearchRequest) {
        console.log(`StrategicDirectorAgent: Detected research request, delegating to ResearchInsightsAgent`);
        return await this.handleResearchRequest(request);
      }

      // Extract category from context if available, otherwise use provided category or default
      let documentCategory = category;

      // Try to extract category from context if not provided
      if (!documentCategory && context) {
        const categoryMatch = context.match(/Source Document Categories:\s*([^\n]+)/i);
        if (categoryMatch) {
          documentCategory = categoryMatch[1].trim();
          console.log(`StrategicDirectorAgent: Extracted category from context: "${documentCategory}"`);
        } else {
          // Also try to extract from "Additional Context" section
          const additionalContextMatch = context.match(/Additional Context[^:]*:\s*[^-]*-\s*Source Document Categories:\s*([^\n]+)/i);
          if (additionalContextMatch) {
            documentCategory = additionalContextMatch[1].trim();
            console.log(`StrategicDirectorAgent: Extracted category from additional context: "${documentCategory}"`);
          }
        }
      }

      // Fall back to default if still no category
      if (!documentCategory) {
        documentCategory = 'strategic-analysis';
        console.log(`StrategicDirectorAgent: Using default category: "${documentCategory}"`);
      } else {
        console.log(`StrategicDirectorAgent: Using category "${documentCategory}" for document queries`);
      }

      // Step 1: Use QuestionAnswerAgent to assess information sufficiency
      console.log(`StrategicDirectorAgent: Using QuestionAnswerAgent to assess information sufficiency`);
      const qaResult = await this.questionAnswerAgent.process({
        userRequest: request,
        context,
        userId: 'strategic-director-agent',
        category: documentCategory,
        maxQuestions: 1
      });

      // Check if QuestionAnswerAgent indicates insufficient information
      const hasInsufficientInfo = this.detectInsufficientInformation(qaResult);

      if (hasInsufficientInfo) {
        console.log(`StrategicDirectorAgent: QuestionAnswerAgent detected insufficient information - STOPPING analysis and providing guidance`);
        return await this.generateInformationRequestResponse(qaResult);
      }

      // Check if the response indicates specific information gaps that require team delegation
      const hasInformationGaps = this.detectInformationGaps(qaResult);

      if (hasInformationGaps) {
        console.log(`StrategicDirectorAgent: Detected specific information gaps requiring team delegation - STOPPING analysis and delegating`);
        return this.generateTeamDelegationResponse(qaResult);
      }

      console.log(`StrategicDirectorAgent: Information sufficient, proceeding with strategic analysis`);
      return await this.generateStrategicAnalysis(request, context);
    } catch (error) {
      console.error(`Error processing request in ${this.name}:`, error);
      return `I encountered an error while processing your request. Please try again.`;
    }
  }

  /**
   * Detect if QuestionAnswerAgent indicates insufficient information
   */
  private detectInsufficientInformation(qaResult: any): boolean {
    console.log(`StrategicDirectorAgent: Checking for insufficient information in qaResult:`, {
      success: qaResult.success,
      error: qaResult.error,
      summary: qaResult.summary?.substring(0, 200) + '...',
      questionsLength: qaResult.questions?.length
    });

    // Check for explicit insufficient information indicators
    if (!qaResult.success && qaResult.error === "Insufficient context for answering question") {
      console.log(`StrategicDirectorAgent: Detected insufficient info via explicit error`);
      return true;
    }

    // Check if the summary or response content indicates insufficient information
    const responseText = qaResult.summary || qaResult.questions?.[0]?.answer || '';
    const lowerResponse = responseText.toLowerCase();

    const insufficientIndicators = [
      'i don\'t have enough information',
      'insufficient information',
      'not enough context',
      'i need more details',
      'please provide more information',
      'i couldn\'t find specific information',
      'no relevant information found',
      'unable to find information',
      'i don\'t have access to',
      'no information available',
      'cannot provide a specific answer',
      'more context would be helpful',
      'i would need additional information',
      'i need more specific information'
    ];

    const foundIndicator = insufficientIndicators.find(indicator => lowerResponse.includes(indicator));
    if (foundIndicator) {
      console.log(`StrategicDirectorAgent: Detected insufficient info via indicator: "${foundIndicator}"`);
      return true;
    }

    console.log(`StrategicDirectorAgent: No insufficient information indicators found`);
    return false;
  }

  /**
   * Detect and structure information gaps requiring team delegation
   */
  private detectInformationGaps(qaResult: any): boolean {
    const gapAnalysis = this.analyzeInformationGaps(qaResult);

    if (gapAnalysis.hasGaps) {
      console.log(`StrategicDirectorAgent: Detected ${gapAnalysis.totalGaps} information gaps requiring team delegation`);
      console.log(`StrategicDirectorAgent: Gap categories: ${gapAnalysis.identifiedGaps.map(g => g.category).join(', ')}`);
      // Store the analysis for use in delegation response
      this.lastGapAnalysis = gapAnalysis;
      return true;
    }

    return false;
  }

  /**
   * ENHANCED: Analyze and structure information gaps from QuestionAnswerAgent response
   * This method now properly parses actual response patterns and extracts specific missing context items
   */
  private analyzeInformationGaps(qaResult: any): InformationGapAnalysis {
    const responseText = qaResult.summary || qaResult.questions?.[0]?.answer || '';
    const lowerResponse = responseText.toLowerCase();

    console.log(`StrategicDirectorAgent: Analyzing response for information gaps (${responseText.length} chars)`);
    console.log(`StrategicDirectorAgent: Response preview: "${responseText.substring(0, 200)}..."`);

    const identifiedGaps: InformationGap[] = [];

    // ENHANCED: Parse actual missing context items from QuestionAnswerAgent responses
    const missingContextItems = this.extractMissingContextItems(responseText);
    console.log(`StrategicDirectorAgent: Extracted ${missingContextItems.length} missing context items:`, missingContextItems);

    // ENHANCED: Map missing context items to structured gaps
    missingContextItems.forEach(item => {
      const gap = this.mapContextItemToGap(item);
      if (gap) {
        identifiedGaps.push(gap);
      }
    });

    // ENHANCED: Check for specific gap patterns in the response
    const patternGaps = this.detectGapPatterns(responseText, lowerResponse);
    patternGaps.forEach(gap => {
      // Avoid duplicates
      if (!identifiedGaps.some(existing => existing.category === gap.category)) {
        identifiedGaps.push(gap);
      }
    });

    // ENHANCED: Check for general information gap indicators
    const generalGapIndicators = [
      'missing context includes',
      'i need more specific information',
      'need more specific information',
      'please provide specific details',
      'without these specifics',
      'would make the campaign much more credible',
      'to receive a more effective',
      'insufficient information',
      'not enough context',
      'i don\'t have enough information',
      'please provide more information',
      'i couldn\'t find specific information',
      'no relevant information found',
      'unable to find information',
      'more context would be helpful',
      'i would need additional information'
    ];

    const hasGeneralGaps = generalGapIndicators.some(indicator => lowerResponse.includes(indicator));

    // If we have general gaps but no specific gaps identified, create default gaps
    if (hasGeneralGaps && identifiedGaps.length === 0) {
      console.log(`StrategicDirectorAgent: General gaps detected but no specific gaps found, creating default gaps`);
      identifiedGaps.push(...this.createDefaultInformationGaps());
    }

    const analysis = {
      hasGaps: identifiedGaps.length > 0 || hasGeneralGaps,
      identifiedGaps,
      summary: responseText,
      totalGaps: identifiedGaps.length
    };

    console.log(`StrategicDirectorAgent: Gap analysis complete - ${analysis.totalGaps} gaps identified, hasGaps: ${analysis.hasGaps}`);
    return analysis;
  }

  /**
   * Extract specific missing context items from QuestionAnswerAgent response
   */
  private extractMissingContextItems(responseText: string): string[] {
    const missingItems: string[] = [];

    // Pattern 1: "Missing context includes:" followed by list
    const missingContextMatch = responseText.match(/Missing context includes?:?\s*([\s\S]*?)(?:\n\n|\n(?=[A-Z])|$)/i);
    if (missingContextMatch) {
      const contextSection = missingContextMatch[1];
      // Extract bullet points or numbered items
      const items = contextSection.match(/[-•*]\s*([^\n]+)/g) || contextSection.match(/\d+\.\s*([^\n]+)/g);
      if (items) {
        items.forEach(item => {
          const cleanItem = item.replace(/^[-•*\d\.\s]+/, '').trim();
          if (cleanItem.length > 5) {
            missingItems.push(cleanItem);
          }
        });
      }
    }

    // Pattern 2: "I need more specific information about:" followed by list
    const needInfoMatch = responseText.match(/I need more specific information about:?\s*([\s\S]*?)(?:\n\n|\n(?=[A-Z])|$)/i);
    if (needInfoMatch) {
      const infoSection = needInfoMatch[1];
      const items = infoSection.match(/[-•*]\s*([^\n]+)/g) || infoSection.match(/\d+\.\s*([^\n]+)/g);
      if (items) {
        items.forEach(item => {
          const cleanItem = item.replace(/^[-•*\d\.\s]+/, '').trim();
          if (cleanItem.length > 5) {
            missingItems.push(cleanItem);
          }
        });
      }
    }

    // Pattern 3: "Please provide specific details about:" followed by list
    const provideDetailsMatch = responseText.match(/Please provide specific details about:?\s*([\s\S]*?)(?:\n\n|\n(?=[A-Z])|$)/i);
    if (provideDetailsMatch) {
      const detailsSection = provideDetailsMatch[1];
      const items = detailsSection.match(/[-•*]\s*([^\n]+)/g) || detailsSection.match(/\d+\.\s*([^\n]+)/g);
      if (items) {
        items.forEach(item => {
          const cleanItem = item.replace(/^[-•*\d\.\s]+/, '').trim();
          if (cleanItem.length > 5) {
            missingItems.push(cleanItem);
          }
        });
      }
    }

    // Pattern 4: Extract from QuestionAnswerAgent missingContext array
    if (responseText.includes('missingContext')) {
      try {
        const jsonMatch = responseText.match(/\{[\s\S]*"missingContext":\s*\[([\s\S]*?)\][\s\S]*\}/);
        if (jsonMatch) {
          const missingContextArray = JSON.parse(`[${jsonMatch[1]}]`);
          missingItems.push(...missingContextArray.filter((item: any) => typeof item === 'string' && item.length > 5));
        }
      } catch (error) {
        console.log(`StrategicDirectorAgent: Could not parse missingContext JSON:`, error);
      }
    }

    return [...new Set(missingItems)]; // Remove duplicates
  }

  /**
   * Map a missing context item to a structured InformationGap
   */
  private mapContextItemToGap(contextItem: string): InformationGap | null {
    const lowerItem = contextItem.toLowerCase();

    // Product-related gaps
    if (lowerItem.includes('product') || lowerItem.includes('feature') || lowerItem.includes('saas') || lowerItem.includes('knowledge explorer')) {
      return {
        category: 'Product Information',
        description: `Detailed information about: ${contextItem}`,
        priority: 'HIGH',
        assignedTeam: 'Business Analysis Team',
        specificRequirements: [
          `Research and document: ${contextItem}`,
          'Provide technical specifications and capabilities',
          'Identify unique selling points and differentiators',
          'Document integration capabilities and requirements'
        ],
        deliverable: `Product Information Report: ${contextItem}`,
        timeline: '1 week'
      };
    }

    // Market/competitive gaps
    if (lowerItem.includes('market') || lowerItem.includes('competitive') || lowerItem.includes('competitor') || lowerItem.includes('industry')) {
      return {
        category: 'Market Intelligence',
        description: `Market research and analysis for: ${contextItem}`,
        priority: 'HIGH',
        assignedTeam: 'Research Team',
        specificRequirements: [
          `Conduct research on: ${contextItem}`,
          'Analyze competitive landscape and positioning',
          'Identify market trends and opportunities',
          'Assess market size and growth potential'
        ],
        deliverable: `Market Analysis Report: ${contextItem}`,
        timeline: '2 weeks'
      };
    }

    // Customer/audience gaps
    if (lowerItem.includes('customer') || lowerItem.includes('audience') || lowerItem.includes('demographic') || lowerItem.includes('persona')) {
      return {
        category: 'Customer Intelligence',
        description: `Customer research and analysis for: ${contextItem}`,
        priority: 'HIGH',
        assignedTeam: 'Research Team',
        specificRequirements: [
          `Research and analyze: ${contextItem}`,
          'Develop detailed customer personas',
          'Map customer journey and touchpoints',
          'Validate pain points through surveys/interviews'
        ],
        deliverable: `Customer Intelligence Report: ${contextItem}`,
        timeline: '2 weeks'
      };
    }

    // Budget/resource gaps
    if (lowerItem.includes('budget') || lowerItem.includes('cost') || lowerItem.includes('resource') || lowerItem.includes('timeline')) {
      return {
        category: 'Budget and Resources',
        description: `Budget and resource planning for: ${contextItem}`,
        priority: 'MEDIUM',
        assignedTeam: 'Marketing Team',
        specificRequirements: [
          `Define and document: ${contextItem}`,
          'Allocate budget across channels and activities',
          'Assess resource availability and requirements',
          'Establish timeline and milestone budgeting'
        ],
        deliverable: `Budget and Resource Plan: ${contextItem}`,
        timeline: '1 week'
      };
    }

    // Metrics/performance gaps
    if (lowerItem.includes('metric') || lowerItem.includes('kpi') || lowerItem.includes('benchmark') || lowerItem.includes('measurement')) {
      return {
        category: 'Performance Metrics',
        description: `Performance metrics and benchmarking for: ${contextItem}`,
        priority: 'HIGH',
        assignedTeam: 'Business Analysis Team',
        specificRequirements: [
          `Define and establish: ${contextItem}`,
          'Set baseline metrics and benchmarks',
          'Create measurement methodology',
          'Establish success criteria and KPIs'
        ],
        deliverable: `Performance Metrics Framework: ${contextItem}`,
        timeline: '1 week'
      };
    }

    // Marketing infrastructure gaps
    if (lowerItem.includes('channel') || lowerItem.includes('marketing') || lowerItem.includes('campaign') || lowerItem.includes('content')) {
      return {
        category: 'Marketing Infrastructure',
        description: `Marketing infrastructure assessment for: ${contextItem}`,
        priority: 'MEDIUM',
        assignedTeam: 'Marketing Team',
        specificRequirements: [
          `Assess and document: ${contextItem}`,
          'Evaluate current marketing capabilities',
          'Identify gaps in marketing infrastructure',
          'Recommend improvements and optimizations'
        ],
        deliverable: `Marketing Infrastructure Assessment: ${contextItem}`,
        timeline: '1 week'
      };
    }

    // Default gap for unmatched items
    return {
      category: 'Market Intelligence',
      description: `Research and analysis required for: ${contextItem}`,
      priority: 'MEDIUM',
      assignedTeam: 'Research Team',
      specificRequirements: [
        `Conduct comprehensive research on: ${contextItem}`,
        'Gather relevant data and insights',
        'Analyze findings and implications',
        'Provide actionable recommendations'
      ],
      deliverable: `Research Report: ${contextItem}`,
      timeline: '1 week'
    };
  }

  /**
   * Detect specific gap patterns in the response text
   */
  private detectGapPatterns(_responseText: string, lowerResponse: string): InformationGap[] {
    const gaps: InformationGap[] = [];

    // Pattern-based detection for common strategic analysis gaps
    const patterns = [
      {
        keywords: ['product features', 'saas knowledge explorer', 'unique selling points', 'product specifications'],
        gap: {
          category: 'Product Information',
          description: 'Comprehensive product analysis and documentation',
          priority: 'HIGH' as const,
          assignedTeam: 'Business Analysis Team',
          specificRequirements: [
            'Document all product features and capabilities',
            'Identify unique selling points and differentiators',
            'Create technical specifications document',
            'Analyze integration capabilities and requirements'
          ],
          deliverable: 'Complete Product Analysis Document',
          timeline: '1 week'
        }
      },
      {
        keywords: ['competitive landscape', 'market research', 'competitor analysis', 'industry trends'],
        gap: {
          category: 'Market Intelligence',
          description: 'Comprehensive market and competitive analysis',
          priority: 'HIGH' as const,
          assignedTeam: 'Research Team',
          specificRequirements: [
            'Conduct competitive landscape analysis',
            'Research market size and growth projections',
            'Identify industry trends and dynamics',
            'Analyze competitor positioning and strategies'
          ],
          deliverable: 'Market Intelligence Report',
          timeline: '2 weeks'
        }
      },
      {
        keywords: ['target audience demographics', 'customer personas', 'user behavior', 'customer segments'],
        gap: {
          category: 'Customer Intelligence',
          description: 'Target audience research and persona development',
          priority: 'HIGH' as const,
          assignedTeam: 'Research Team',
          specificRequirements: [
            'Research target audience demographics',
            'Develop detailed customer personas',
            'Analyze customer behavior patterns',
            'Map customer journey and touchpoints'
          ],
          deliverable: 'Customer Intelligence and Persona Report',
          timeline: '2 weeks'
        }
      },
      {
        keywords: ['budget limitations', 'marketing budget', 'resource constraints', 'cost analysis'],
        gap: {
          category: 'Budget and Resources',
          description: 'Marketing budget and resource planning',
          priority: 'MEDIUM' as const,
          assignedTeam: 'Marketing Team',
          specificRequirements: [
            'Define total marketing budget allocation',
            'Distribute budget across channels and activities',
            'Assess resource availability and constraints',
            'Create timeline and milestone budgeting'
          ],
          deliverable: 'Marketing Budget and Resource Plan',
          timeline: '1 week'
        }
      },
      {
        keywords: ['performance metrics', 'kpis', 'success criteria', 'measurement framework'],
        gap: {
          category: 'Performance Metrics',
          description: 'Performance measurement and KPI framework',
          priority: 'HIGH' as const,
          assignedTeam: 'Business Analysis Team',
          specificRequirements: [
            'Define key performance indicators (KPIs)',
            'Establish baseline metrics and benchmarks',
            'Create measurement methodology',
            'Set success criteria and targets'
          ],
          deliverable: 'Performance Metrics and KPI Framework',
          timeline: '1 week'
        }
      }
    ];

    // Check each pattern against the response
    patterns.forEach(pattern => {
      const hasKeywords = pattern.keywords.some(keyword => lowerResponse.includes(keyword.toLowerCase()));
      if (hasKeywords) {
        gaps.push(pattern.gap);
      }
    });

    return gaps;
  }

  /**
   * Create default information gaps when general gaps are detected but no specific gaps found
   */
  private createDefaultInformationGaps(): InformationGap[] {
    return [
      {
        category: 'Product Information',
        description: 'Comprehensive product analysis and documentation',
        priority: 'HIGH',
        assignedTeam: 'Business Analysis Team',
        specificRequirements: [
          'Document product features and capabilities',
          'Identify unique selling points',
          'Create technical specifications',
          'Analyze competitive advantages'
        ],
        deliverable: 'Product Analysis Document',
        timeline: '1 week'
      },
      {
        category: 'Market Intelligence',
        description: 'Market research and competitive analysis',
        priority: 'HIGH',
        assignedTeam: 'Research Team',
        specificRequirements: [
          'Conduct market size analysis',
          'Research competitive landscape',
          'Identify industry trends',
          'Analyze market opportunities'
        ],
        deliverable: 'Market Research Report',
        timeline: '2 weeks'
      },
      {
        category: 'Customer Intelligence',
        description: 'Target audience research and customer insights',
        priority: 'HIGH',
        assignedTeam: 'Research Team',
        specificRequirements: [
          'Research target audience demographics',
          'Develop customer personas',
          'Analyze customer behavior',
          'Map customer journey'
        ],
        deliverable: 'Customer Intelligence Report',
        timeline: '2 weeks'
      },
      {
        category: 'Marketing Infrastructure',
        description: 'Marketing capabilities and channel assessment',
        priority: 'MEDIUM',
        assignedTeam: 'Marketing Team',
        specificRequirements: [
          'Assess current marketing channels',
          'Evaluate marketing technology stack',
          'Analyze content creation capabilities',
          'Review brand guidelines and messaging'
        ],
        deliverable: 'Marketing Infrastructure Assessment',
        timeline: '1 week'
      }
    ];
  }

  // Store the last gap analysis for use in delegation response
  private lastGapAnalysis: InformationGapAnalysis | null = null;

  /**
   * LLM-ONLY: Parse Strategic Director output using pure intelligent extraction
   * Uses OpenAI o3-mini-2025-01-31 to comprehensively extract all tasks and assignments
   * No regex fallbacks - fully committed to LLM-based approach
   */
  public async parseStrategicDirectorOutput(outputText: string): Promise<StrategicTaskCollection> {
    console.log(`StrategicDirectorAgent: Parsing Strategic Director output (${outputText.length} chars)`);

    const currentDate = new Date();
    const collectionId = `parsed-collection-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;

    // Extract ALL tasks using enhanced LLM (comprehensive extraction)
    const extractedTasks = await this.extractTeamAssignments(outputText);
    console.log(`StrategicDirectorAgent: LLM extracted ${extractedTasks.length} tasks (comprehensive extraction)`);

    // Deduplicate tasks (LLM should avoid duplicates, but ensure clean output)
    const uniqueTasks = this.deduplicateTasks(extractedTasks);
    console.log(`StrategicDirectorAgent: Final task count after deduplication: ${uniqueTasks.length}`);

    // Calculate team assignments
    const teamAssignmentSummary: { [teamName: string]: { taskCount: number; taskIds: string[]; estimatedWorkload: string; } } = {};
    uniqueTasks.forEach(task => {
      if (!teamAssignmentSummary[task.assignedTeam]) {
        teamAssignmentSummary[task.assignedTeam] = {
          taskCount: 0,
          taskIds: [],
          estimatedWorkload: '0 weeks'
        };
      }
      teamAssignmentSummary[task.assignedTeam].taskCount++;
      teamAssignmentSummary[task.assignedTeam].taskIds.push(task.id);
    });

    // Calculate workload for each team
    Object.keys(teamAssignmentSummary).forEach(team => {
      const teamTasks = uniqueTasks.filter(task => task.assignedTeam === team);
      const totalWeeks = teamTasks.reduce((sum, task) => {
        return sum + this.parseTimelineToWeeks(task.timeline.estimatedDuration);
      }, 0);
      teamAssignmentSummary[team].estimatedWorkload = `${totalWeeks} weeks`;
    });

    const collection: StrategicTaskCollection = {
      collectionId,
      name: 'Strategic Director Output Analysis',
      description: 'Tasks extracted from Strategic Director Agent output',
      source: 'Strategic Analysis',
      totalTasks: uniqueTasks.length,
      tasks: uniqueTasks,
      overallTimeline: {
        startDate: currentDate,
        estimatedEndDate: new Date(Math.max(...uniqueTasks.map(task =>
          task.timeline.dueDate?.getTime() || currentDate.getTime()
        ))),
        criticalPath: this.calculateCriticalPath(uniqueTasks).criticalPath
      },
      teamAssignments: teamAssignmentSummary,
      successMetrics: this.generateSuccessMetricsFromOutput(outputText, uniqueTasks),
      createdAt: currentDate,
      updatedAt: currentDate
    };

    return collection;
  }

  /**
   * LLM-ONLY: Extract ALL tasks from Strategic Director output using OpenAI o3-mini-2025-01-31
   * Comprehensively handles team assignments, implementation tasks, and all actionable items
   * No regex fallbacks - pure intelligent extraction
   */
  private async extractTeamAssignments(outputText: string): Promise<StrategicTask[]> {
    const tasks: StrategicTask[] = [];
    const currentDate = new Date();

    console.log(`StrategicDirectorAgent: Starting LLM-based team assignment extraction from ${outputText.length} chars of output`);

    try {
      // Use OpenAI o3-mini-2025-01-31 for comprehensive intelligent extraction
      const extractionPrompt = `
You are an expert at parsing Strategic Director output and extracting ALL tasks, assignments, and actionable items. Your task is to analyze the provided Strategic Director output text comprehensively and extract every possible task, whether explicitly assigned to teams or not.

STRATEGIC DIRECTOR OUTPUT:
${outputText}

COMPREHENSIVE EXTRACTION REQUIREMENTS:
1. Find ALL team assignments, tasks, and delegated work items from ANY section
2. Look for sections like:
   - "TEAM ASSIGNMENTS", "4. TEAM ASSIGNMENTS"
   - "IMPLEMENTATION PLAN", "Implementation Plan", "## Implementation"
   - Numbered task lists (1., 2., 3., etc.)
   - Bullet points (•, -, *)
   - Action items and next steps
   - Strategic recommendations that require execution
3. Extract the following information for each task:
   - Task title/name
   - Task description
   - Assigned team (Research Team, Marketing Team, Business Analysis Team, Sales Team, Software Design Team, etc.)
   - Priority level (if mentioned)
   - Timeline/duration (if mentioned)
   - Specific requirements or deliverables
   - Any dependencies or rationale

AVAILABLE TEAMS:
- Research Team (market research, competitive analysis, data gathering)
- Marketing Team (campaigns, branding, marketing strategy)
- Business Analysis Team (business processes, requirements, analysis)
- Sales Team (sales strategy, customer acquisition, revenue)
- Software Design Team (development, UI/UX, technical implementation)
- Strategic Director (high-level planning, coordination, oversight)
- Content Team (content creation, documentation, communications)

TEAM ASSIGNMENT LOGIC:
- If team is explicitly mentioned, use that team
- If no team specified, infer based on task content:
  * Research/analysis/data tasks → Research Team
  * Marketing/branding/campaigns → Marketing Team
  * Business process/requirements → Business Analysis Team
  * Sales/revenue/customer tasks → Sales Team
  * Development/technical/design → Software Design Team
  * Strategic/planning/coordination → Strategic Director
  * Content/documentation/writing → Content Team

RESPONSE FORMAT:
Return a JSON array of task objects. Each task object should have this structure:
{
  "title": "Task title",
  "description": "Detailed task description",
  "assignedTeam": "Team Name",
  "priority": "HIGH|MEDIUM|LOW",
  "timeline": "estimated duration (e.g., '2 weeks', '1 week')",
  "specificRequirements": ["requirement 1", "requirement 2", "..."],
  "deliverable": "What should be delivered",
  "rationale": "Why this team was chosen (if provided)"
}

CRITICAL GUIDELINES:
- Extract ALL actionable items, including implementation tasks, research tasks, analysis tasks, etc.
- Be comprehensive - capture every task that requires execution, regardless of format or section
- If priority is not explicitly stated, infer from context (urgent/critical = HIGH, important = MEDIUM, nice-to-have = LOW)
- If timeline is not stated, estimate based on task complexity (simple = 1 week, moderate = 2 weeks, complex = 3-4 weeks)
- Include standalone implementation items that may not have explicit team assignments
- Capture both formal team assignments AND informal action items
- Look for tasks in conclusions, recommendations, and next steps sections

Return ONLY the JSON array, no additional text or explanation.
`;

      const llmResponse = await this.llmTool.processContent({
        prompt: extractionPrompt,
        model: 'o3-mini-2025-01-31',
        provider: 'openai',
        modelOptions: {
          temperature: 0.1, // Low temperature for consistent extraction
          max_tokens: 4000
        }
      });

      console.log(`StrategicDirectorAgent: Received LLM response for team assignment extraction (${llmResponse.length} chars)`);

      // Parse the JSON response
      let extractedTasks: any[] = [];
      try {
        // Try to extract JSON from the response
        const jsonMatch = llmResponse.match(/\[[\s\S]*\]/);
        if (jsonMatch) {
          extractedTasks = JSON.parse(jsonMatch[0]);
        } else {
          // Fallback: try to parse the entire response as JSON
          extractedTasks = JSON.parse(llmResponse);
        }
      } catch (parseError) {
        console.error(`StrategicDirectorAgent: Failed to parse LLM JSON response:`, parseError);
        console.log(`StrategicDirectorAgent: Raw LLM response:`, llmResponse.substring(0, 500));
        // Return empty array if LLM parsing fails
        console.warn(`StrategicDirectorAgent: LLM extraction failed, returning empty task array`);
        return [];
      }

      console.log(`StrategicDirectorAgent: Successfully parsed ${extractedTasks.length} tasks from LLM response`);

      // Convert extracted tasks to StrategicTask objects
      for (const [index, extractedTask] of extractedTasks.entries()) {
        try {
          const task: StrategicTask = {
            id: `llm-extracted-task-${Date.now()}-${index}-${Math.random().toString(36).substring(2, 11)}`,
            title: extractedTask.title || `Task ${index + 1}`,
            description: extractedTask.description || extractedTask.title || '',
            category: this.mapTeamToCategory(extractedTask.assignedTeam || 'Research Team'),
            priority: this.mapPriorityText(extractedTask.priority || 'MEDIUM'),
            assignedTeam: this.normalizeTeamName(extractedTask.assignedTeam || 'Research Team') as any,
            status: 'IDENTIFIED',
            specificRequirements: Array.isArray(extractedTask.specificRequirements)
              ? extractedTask.specificRequirements
              : [extractedTask.description || extractedTask.title],
            deliverable: extractedTask.deliverable || `Completed: ${extractedTask.title}`,
            timeline: {
              estimatedDuration: extractedTask.timeline || '2 weeks',
              startDate: currentDate,
              dueDate: new Date(currentDate.getTime() + (this.parseTimelineToWeeks(extractedTask.timeline || '2 weeks') * 7 * 24 * 60 * 60 * 1000))
            },
            successCriteria: [
              `Complete all requirements: ${Array.isArray(extractedTask.specificRequirements) ? extractedTask.specificRequirements.join(', ') : extractedTask.description}`,
              `Deliver ${extractedTask.deliverable || extractedTask.title}`,
              'Meet quality standards for strategic analysis'
            ],
            metadata: {
              createdAt: currentDate,
              createdBy: 'Strategic Director LLM Parser',
              updatedAt: currentDate,
              source: 'Strategic Planning'
            }
          };

          tasks.push(task);
          console.log(`StrategicDirectorAgent: Created task "${task.title}" assigned to ${task.assignedTeam}`);
        } catch (taskError) {
          console.error(`StrategicDirectorAgent: Error creating task from extracted data:`, taskError, extractedTask);
        }
      }

      console.log(`StrategicDirectorAgent: Successfully created ${tasks.length} StrategicTask objects from LLM extraction`);
      return tasks;

    } catch (error) {
      console.error(`StrategicDirectorAgent: Error in LLM-based task extraction:`, error);
      console.warn(`StrategicDirectorAgent: LLM extraction failed completely, returning empty task array`);

      // Return empty array if LLM fails completely
      return [];
    }
  }



  /**
   * Normalize team names to standard format - ABSTRACTED TO UTILITY
   */
  private normalizeTeamName(teamName: string): string {
    return StrategicTaskMapper.normalizeTeamName(teamName);
  }



  /**
   * Enhanced deduplication with analytics - ABSTRACTED TO UTILITY
   */
  private deduplicateTasks(tasks: StrategicTask[]): StrategicTask[] {
    return StrategicTaskUtils.deduplicateTasks(tasks);
  }



  private mapTeamToCategory(teamName: string): StrategicTask['category'] {
    return StrategicTaskMapper.mapTeamToCategory(teamName) as StrategicTask['category'];
  }

  private mapPriorityText(priorityText: string): StrategicTask['priority'] {
    return StrategicTaskMapper.mapPriorityText(priorityText) as StrategicTask['priority'];
  }

  private generateSuccessMetricsFromOutput(outputText: string, tasks: StrategicTask[]): string[] {
    return StrategicTaskFormatter.generateSuccessMetricsFromOutput(outputText, tasks);
  }

  // Comprehensive task management system
  private strategicTasks: StrategicTask[] = [];
  private taskCollections: StrategicTaskCollection[] = [];



  /**
   * Parse timeline string to weeks - ABSTRACTED TO UTILITY
   */
  private parseTimelineToWeeks(timeline: string): number {
    return StrategicTaskUtils.parseTimelineToWeeks(timeline);
  }

  /**
   * Map team name to user IDs - ABSTRACTED TO UTILITY
   */
  private mapTeamToUserIds(teamName: string): string[] {
    return StrategicTaskMapper.mapTeamToUserIds(teamName);
  }

  /**
   * Map information gap priority to task priority - ABSTRACTED TO UTILITY
   */
  private mapPriorityToTaskPriority(gapPriority: 'HIGH' | 'MEDIUM' | 'LOW'): 'High' | 'Medium' | 'Low' {
    return StrategicTaskMapper.mapPriorityToTaskPriority(gapPriority);
  }

  /**
   * Get the current information gap analysis for external use
   */
  public getCurrentGapAnalysis(): InformationGapAnalysis | null {
    return this.lastGapAnalysis;
  }

  /**
   * MAIN INTEGRATION METHOD: Convert StrategicTaskCollection to standard Task format - ABSTRACTED TO UTILITY
   * This ensures compatibility with existing createTasksAgent, pmoProjectsTaskAgent, and API endpoints
   */
  public convertStrategicTasksToStandardTasks(collection: StrategicTaskCollection, projectId: string): Array<Omit<Task, 'id'>> {
    return StrategicTaskConverter.convertStrategicTasksToStandardTasks(collection, projectId);
  }

  /**
   * ENHANCED LEGACY METHOD: Now uses comprehensive task collection system
   * This maintains backward compatibility while leveraging the new JSON structure
   */
  public async convertGapsToTasks(projectId: string): Promise<Array<Omit<Task, 'id'>>> {
    console.log('StrategicDirectorAgent: convertGapsToTasks - Using enhanced task collection system');

    if (!this.lastGapAnalysis || this.lastGapAnalysis.identifiedGaps.length === 0) {
      console.log('StrategicDirectorAgent: No gaps identified, returning empty task array');
      return [];
    }

    // Create comprehensive task collection
    const context: TaskGenerationContext = {
      userRequest: 'Legacy task conversion from information gaps',
      analysisContent: 'Converting identified information gaps to actionable tasks',
      pmoContext: {
        pmoId: projectId,
        projectTitle: 'Strategic Analysis Project',
        priority: 'HIGH',
        category: 'Strategic Analysis'
      },
      identifiedGaps: this.lastGapAnalysis.identifiedGaps,
      strategicObjectives: [
        'Resolve all identified information gaps',
        'Enable comprehensive strategic analysis',
        'Provide actionable team assignments'
      ]
    };

    const collection = await this.createStrategicTaskCollection(context);

    // Convert to standard Task format for compatibility
    return this.convertStrategicTasksToStandardTasks(collection, projectId);
  }

  /**
   * Format comprehensive task description from StrategicTask - ABSTRACTED TO UTILITY
   */
  private formatTaskDescription(strategicTask: StrategicTask): string {
    return StrategicTaskFormatter.formatTaskDescription(strategicTask);
  }

  /**
   * Format comprehensive task notes from StrategicTask and collection context - ABSTRACTED TO UTILITY
   */
  private formatTaskNotes(strategicTask: StrategicTask, collection: StrategicTaskCollection): string {
    return StrategicTaskFormatter.formatTaskNotes(strategicTask, collection);
  }

  /**
   * Map StrategicTask status to standard TaskStatus - ABSTRACTED TO UTILITY
   */
  private mapStrategicStatusToTaskStatus(strategicStatus: StrategicTask['status']): TaskStatus {
    return StrategicTaskMapper.mapStrategicStatusToTaskStatus(strategicStatus as any);
  }

  /**
   * Map StrategicTask priority to standard TaskPriority - ABSTRACTED TO UTILITY
   */
  private mapStrategicPriorityToTaskPriority(strategicPriority: StrategicTask['priority']): TaskPriority {
    return StrategicTaskMapper.mapStrategicPriorityToTaskPriority(strategicPriority as any);
  }

  /**
   * Create comprehensive strategic task collection from any context
   * This is the MAIN method for ALL Strategic Director task creation
   */
  public async createStrategicTaskCollection(context: TaskGenerationContext): Promise<StrategicTaskCollection> {
    const collectionId = `strategic-collection-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
    const currentDate = new Date();

    // Generate tasks from information gaps if available
    const gapTasks = this.lastGapAnalysis ? this.createTasksFromGaps(context) : [];

    // Generate additional strategic tasks based on context (now async)
    const strategicTasks = await this.createStrategicPlanningTasks(context);

    // Combine all tasks
    const allTasks = [...gapTasks, ...strategicTasks];

    // Calculate overall timeline
    const earliestStart = currentDate;
    const latestEnd = new Date(Math.max(...allTasks.map(task =>
      task.timeline.dueDate?.getTime() || currentDate.getTime()
    )));

    // Analyze team assignments
    const teamAssignments: { [teamName: string]: { taskCount: number; taskIds: string[]; estimatedWorkload: string; } } = {};
    allTasks.forEach(task => {
      if (!teamAssignments[task.assignedTeam]) {
        teamAssignments[task.assignedTeam] = {
          taskCount: 0,
          taskIds: [],
          estimatedWorkload: '0 weeks'
        };
      }
      teamAssignments[task.assignedTeam].taskCount++;
      teamAssignments[task.assignedTeam].taskIds.push(task.id);
    });

    // Calculate workload for each team
    Object.keys(teamAssignments).forEach(team => {
      const teamTasks = allTasks.filter(task => task.assignedTeam === team);
      const totalWeeks = teamTasks.reduce((sum, task) => {
        return sum + this.parseTimelineToWeeks(task.timeline.estimatedDuration);
      }, 0);
      teamAssignments[team].estimatedWorkload = `${totalWeeks} weeks`;
    });

    const collection: StrategicTaskCollection = {
      collectionId,
      name: context.pmoContext ?
        `Strategic Tasks: ${context.pmoContext.projectTitle}` :
        'Strategic Analysis Task Collection',
      description: `Comprehensive task collection generated from strategic analysis: ${context.userRequest.substring(0, 100)}...`,
      source: context.pmoContext ? 'PMO Project' : 'Strategic Analysis',
      totalTasks: allTasks.length,
      tasks: allTasks,
      overallTimeline: {
        startDate: earliestStart,
        estimatedEndDate: latestEnd,
        criticalPath: this.calculateCriticalPath(allTasks).criticalPath
      },
      teamAssignments,
      successMetrics: this.generateSuccessMetrics(context, allTasks),
      createdAt: currentDate,
      updatedAt: currentDate
    };

    // Store the collection
    this.taskCollections.push(collection);
    this.strategicTasks.push(...allTasks);

    return collection;
  }

  /**
   * Create strategic tasks from identified information gaps
   */
  private createTasksFromGaps(context: TaskGenerationContext): StrategicTask[] {
    if (!this.lastGapAnalysis || this.lastGapAnalysis.identifiedGaps.length === 0) {
      return [];
    }

    const tasks: StrategicTask[] = [];
    const currentDate = new Date();

    this.lastGapAnalysis.identifiedGaps.forEach((gap) => {
      // Calculate timeline
      const timelineWeeks = this.parseTimelineToWeeks(gap.timeline);
      const dueDate = new Date(currentDate.getTime() + (timelineWeeks * 7 * 24 * 60 * 60 * 1000));

      // Create main task for the gap
      const mainTask: StrategicTask = {
        id: `task-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
        title: `${gap.category}: ${gap.deliverable}`,
        description: gap.description,
        category: gap.category as any,
        priority: gap.priority === 'HIGH' ? 'HIGH' : gap.priority === 'MEDIUM' ? 'MEDIUM' : 'LOW',
        assignedTeam: gap.assignedTeam as any,
        status: 'IDENTIFIED',
        specificRequirements: gap.specificRequirements,
        deliverable: gap.deliverable,
        timeline: {
          estimatedDuration: gap.timeline,
          startDate: currentDate,
          dueDate: dueDate
        },
        dependencies: gap.dependencies,
        successCriteria: [
          `Complete all specific requirements: ${gap.specificRequirements.join(', ')}`,
          `Deliver ${gap.deliverable} within ${gap.timeline}`,
          'Meet quality standards for strategic analysis input'
        ],
        metadata: {
          createdAt: currentDate,
          createdBy: 'Strategic Director',
          updatedAt: currentDate,
          source: 'Information Gap Analysis',
          pmoId: context.pmoContext?.pmoId,
          projectId: context.pmoContext?.projectTitle
        }
      };

      tasks.push(mainTask);

      // Create subtasks for each specific requirement
      gap.specificRequirements.forEach((requirement, index) => {
        const subtask: StrategicTask = {
          id: `subtask-${Date.now()}-${index}-${Math.random().toString(36).substring(2, 11)}`,
          title: `${gap.assignedTeam}: ${requirement}`,
          description: `Specific requirement for ${gap.category}: ${requirement}`,
          category: gap.category as any,
          priority: gap.priority === 'HIGH' ? 'HIGH' : gap.priority === 'MEDIUM' ? 'MEDIUM' : 'LOW',
          assignedTeam: gap.assignedTeam as any,
          status: 'IDENTIFIED',
          specificRequirements: [requirement],
          deliverable: `Completed: ${requirement}`,
          timeline: {
            estimatedDuration: `${Math.ceil(timelineWeeks / gap.specificRequirements.length)} weeks`,
            startDate: currentDate,
            dueDate: new Date(currentDate.getTime() + (Math.ceil(timelineWeeks / gap.specificRequirements.length) * 7 * 24 * 60 * 60 * 1000))
          },
          dependencies: [mainTask.id],
          successCriteria: [
            `Complete requirement: ${requirement}`,
            'Provide documentation and evidence of completion',
            'Contribute to main deliverable quality'
          ],
          metadata: {
            createdAt: currentDate,
            createdBy: 'Strategic Director',
            updatedAt: currentDate,
            source: 'Information Gap Analysis',
            pmoId: context.pmoContext?.pmoId,
            projectId: context.pmoContext?.projectTitle
          }
        };

        tasks.push(subtask);
      });
    });

    return tasks;
  }

  /**
   * LLM-BASED: Create strategic planning tasks based on context using Claude Sonnet 4
   * Analyzes TaskGenerationContext to create context-aware strategic planning tasks
   */
  private async createStrategicPlanningTasks(context: TaskGenerationContext): Promise<StrategicTask[]> {
    console.log(`StrategicDirectorAgent: Generating LLM-based strategic planning tasks`);

    try {
      // Extract context information for LLM analysis
      const userRequest = context.userRequest || 'Strategic planning task generation';
      const analysisContent = context.analysisContent || '';
      const strategicObjectives = context.strategicObjectives || [];
      const constraints = context.constraints || [];
      const pmoContext = context.pmoContext;
      const identifiedGaps = context.identifiedGaps || [];

      // Primary LLM: Claude Sonnet 4 for strategic planning task generation
      const strategicPlanningPrompt = `
You are a Strategic Director responsible for creating comprehensive strategic planning tasks based on the provided context. Your task is to analyze the context and generate specific, actionable strategic planning tasks.

CONTEXT ANALYSIS:
User Request: ${userRequest}
Analysis Content: ${analysisContent}
Strategic Objectives: ${strategicObjectives.join(', ') || 'Not specified'}
Constraints: ${constraints.join(', ') || 'None specified'}
PMO Context: ${pmoContext ? `Project: ${pmoContext.projectTitle}, Priority: ${pmoContext.priority}, Category: ${pmoContext.category}` : 'No PMO context'}
Information Gaps: ${identifiedGaps.length > 0 ? identifiedGaps.map(gap => gap.category).join(', ') : 'None identified'}

AVAILABLE TEAMS:
- Strategic Director (high-level planning, coordination, synthesis, strategic analysis)
- Research Team (market research, competitive analysis, data gathering, industry trends)
- Business Analysis Team (business processes, requirements, stakeholder analysis, metrics)
- Sales Team (customer insights, pricing, sales channels, market validation)
- Marketing Team (campaigns, branding, customer behavior, marketing channels)
- Software Design Team (technical requirements, implementation, user experience)
- Content Team (content strategy, documentation, communications)

TASK CATEGORIES:
- Strategic Planning (high-level strategic analysis, planning, coordination)
- Implementation (execution planning, resource allocation, project management)
- Market Intelligence (market research, competitive analysis, industry insights)
- Product Analysis (product validation, feature analysis, performance metrics)
- Customer Intelligence (customer research, behavior analysis, feedback)
- Marketing Infrastructure (marketing capabilities, channels, campaigns)
- Performance Metrics (KPIs, measurement, analytics, reporting)
- Customer Validation (customer feedback, market validation, testing)
- Research (general research, data gathering, analysis)
- Content Creation (content strategy, documentation, communications)

RESPONSE FORMAT:
Return a JSON array of strategic task objects. Each task object should have this structure:
{
  "title": "Task title",
  "description": "Detailed task description",
  "category": "Task category from the list above",
  "priority": "CRITICAL|HIGH|MEDIUM|LOW",
  "assignedTeam": "Team name from available teams",
  "timeline": "estimated duration (e.g., '1 week', '2 weeks', '3 days')",
  "specificRequirements": ["requirement 1", "requirement 2", "requirement 3"],
  "deliverable": "What should be delivered",
  "successCriteria": ["criteria 1", "criteria 2", "criteria 3"],
  "dependencies": ["task dependency 1", "task dependency 2"] // Optional
}

CRITICAL REQUIREMENTS:
1. Create strategic planning tasks that are specific to the provided context
2. Ensure tasks are actionable and have clear deliverables
3. Assign teams based on task content and team expertise
4. Set realistic timelines based on task complexity and strategic importance
5. Include both high-level strategic tasks and supporting operational tasks
6. Ensure tasks align with strategic objectives and address identified gaps
7. Create dependencies between tasks where logical sequencing is required
8. Focus on strategic value and business impact

TASK GENERATION GUIDELINES:
- Always include core strategic synthesis and implementation planning tasks
- Add PMO-specific tasks if PMO context is provided
- Create tasks that address identified information gaps
- Include coordination and communication tasks for complex projects
- Ensure task priorities reflect strategic importance and urgency
- Make requirements specific and measurable
- Create realistic timelines that account for dependencies

Return ONLY the JSON array, no additional text or explanation.
`;

      const llmResponse = await this.llmTool.processContent({
        prompt: strategicPlanningPrompt,
        model: 'claude-sonnet-4-********',
        provider: 'anthropic',
        modelOptions: {
          temperature: 0.3, // Moderate temperature for creative but consistent planning
          max_tokens: 4000
        }
      });

      console.log(`StrategicDirectorAgent: Received Claude Sonnet 4 response for strategic planning (${llmResponse.length} chars)`);

      // Parse the JSON response
      let strategicTasks: any[] = [];
      try {
        // Try to extract JSON from the response
        const jsonMatch = llmResponse.match(/\[[\s\S]*\]/);
        if (jsonMatch) {
          strategicTasks = JSON.parse(jsonMatch[0]);
        } else {
          // Fallback: try to parse the entire response as JSON
          strategicTasks = JSON.parse(llmResponse);
        }
      } catch (parseError) {
        console.error(`StrategicDirectorAgent: Failed to parse Claude Sonnet 4 JSON response:`, parseError);
        console.log(`StrategicDirectorAgent: Raw Claude response:`, llmResponse.substring(0, 500));

        // Fallback to Groq DeepSeek
        return await this.createStrategicPlanningTasksWithGroqFallback(context, userRequest, analysisContent, strategicObjectives, constraints, pmoContext, identifiedGaps);
      }

      console.log(`StrategicDirectorAgent: Successfully parsed ${strategicTasks.length} strategic tasks from Claude Sonnet 4`);

      // Convert to StrategicTask objects
      return this.convertLLMTasksToStrategicTasks(strategicTasks, context);

    } catch (error) {
      console.error(`StrategicDirectorAgent: Error in Claude Sonnet 4 strategic planning generation:`, error);
      console.log(`StrategicDirectorAgent: Falling back to Groq DeepSeek`);

      // Fallback to Groq DeepSeek
      const userRequest = context.userRequest || 'Strategic planning task generation';
      const analysisContent = context.analysisContent || '';
      const strategicObjectives = context.strategicObjectives || [];
      const constraints = context.constraints || [];
      const pmoContext = context.pmoContext;
      const identifiedGaps = context.identifiedGaps || [];

      return await this.createStrategicPlanningTasksWithGroqFallback(context, userRequest, analysisContent, strategicObjectives, constraints, pmoContext, identifiedGaps);
    }
  }

  /**
   * Fallback method using Groq DeepSeek for strategic planning task generation
   */
  private async createStrategicPlanningTasksWithGroqFallback(
    context: TaskGenerationContext,
    userRequest: string,
    analysisContent: string,
    strategicObjectives: string[],
    constraints: string[],
    pmoContext: any,
    identifiedGaps: any[]
  ): Promise<StrategicTask[]> {
    console.log(`StrategicDirectorAgent: Using Groq DeepSeek fallback for strategic planning tasks`);

    try {
      // Fallback LLM: Groq DeepSeek for strategic planning task generation
      const fallbackPrompt = `
You are a Strategic Director creating strategic planning tasks. Analyze the context and generate actionable strategic tasks.

CONTEXT:
User Request: ${userRequest}
Analysis Content: ${analysisContent}
Strategic Objectives: ${strategicObjectives.join(', ') || 'Not specified'}
Constraints: ${constraints.join(', ') || 'None specified'}
PMO Context: ${pmoContext ? `Project: ${pmoContext.projectTitle}, Priority: ${pmoContext.priority}` : 'No PMO context'}
Information Gaps: ${identifiedGaps.length > 0 ? identifiedGaps.map(gap => gap.category).join(', ') : 'None identified'}

TEAMS: Strategic Director, Research Team, Business Analysis Team, Sales Team, Marketing Team, Software Design Team, Content Team

CATEGORIES: Strategic Planning, Implementation, Market Intelligence, Product Analysis, Customer Intelligence, Marketing Infrastructure, Performance Metrics, Customer Validation, Research, Content Creation

Return JSON array of tasks with: title, description, category, priority (CRITICAL|HIGH|MEDIUM|LOW), assignedTeam, timeline, specificRequirements[], deliverable, successCriteria[], dependencies[] (optional).

Focus on strategic value and context-specific tasks. Include core strategic synthesis and implementation planning.
`;

      const groqResponse = await this.llmTool.processContent({
        prompt: fallbackPrompt,
        model: 'deepseek-r1-distill-llama-70b',
        provider: 'groq',
        modelOptions: {
          temperature: 0.3,
          max_tokens: 3000
        }
      });

      console.log(`StrategicDirectorAgent: Received Groq DeepSeek response for strategic planning (${groqResponse.length} chars)`);

      // Parse the JSON response
      let strategicTasks: any[] = [];
      try {
        const jsonMatch = groqResponse.match(/\[[\s\S]*\]/);
        if (jsonMatch) {
          strategicTasks = JSON.parse(jsonMatch[0]);
        } else {
          strategicTasks = JSON.parse(groqResponse);
        }
      } catch (parseError) {
        console.error(`StrategicDirectorAgent: Failed to parse Groq DeepSeek JSON response:`, parseError);
        console.log(`StrategicDirectorAgent: Raw Groq response:`, groqResponse.substring(0, 500));

        // Final fallback to static tasks
        return this.createStaticStrategicPlanningTasks(context);
      }

      console.log(`StrategicDirectorAgent: Successfully parsed ${strategicTasks.length} strategic tasks from Groq DeepSeek`);

      // Convert to StrategicTask objects
      return this.convertLLMTasksToStrategicTasks(strategicTasks, context);

    } catch (error) {
      console.error(`StrategicDirectorAgent: Error in Groq DeepSeek fallback:`, error);
      console.log(`StrategicDirectorAgent: Using static fallback tasks`);

      // Final fallback to static tasks
      return this.createStaticStrategicPlanningTasks(context);
    }
  }

  /**
   * Convert LLM-generated task data to StrategicTask objects - ABSTRACTED TO UTILITY
   */
  private convertLLMTasksToStrategicTasks(llmTasks: any[], context: TaskGenerationContext): StrategicTask[] {
    return StrategicTaskConverter.convertLLMTasksToStrategicTasks(llmTasks, context);
  }

  /**
   * Map task category to valid StrategicTask category - ABSTRACTED TO UTILITY
   */
  private mapTaskCategory(category: string): StrategicTask['category'] {
    return StrategicTaskMapper.mapTaskCategory(category) as StrategicTask['category'];
  }

  /**
   * Static fallback for strategic planning tasks when LLM fails - ABSTRACTED TO UTILITY
   */
  private createStaticStrategicPlanningTasks(context: TaskGenerationContext): StrategicTask[] {
    return StrategicTaskConverter.createStaticStrategicPlanningTasks(context);
  }

  /**
   * Calculate critical path for task dependencies - ABSTRACTED TO UTILITY
   */
  private calculateCriticalPath(tasks: StrategicTask[]): { criticalPath: string[]; totalDuration: number; } {
    return StrategicTaskUtils.calculateCriticalPath(tasks);
  }

  /**
   * Generate success metrics for the task collection
   */
  private generateSuccessMetrics(context: TaskGenerationContext, tasks: StrategicTask[]): string[] {
    const metrics = [
      'All tasks completed within estimated timelines',
      'All deliverables meet quality standards',
      'Team coordination and communication effectiveness',
      'Strategic objectives alignment and achievement'
    ];

    // Add context-specific metrics
    if (context.pmoContext) {
      metrics.push(
        'PMO compliance and alignment achieved',
        'Organizational strategic goals supported',
        'Resource utilization efficiency'
      );
    }

    if (this.lastGapAnalysis && this.lastGapAnalysis.identifiedGaps.length > 0) {
      metrics.push(
        'All identified information gaps resolved',
        'Information quality and completeness validated',
        'Strategic analysis confidence level achieved'
      );
    }

    // Add team-specific metrics
    const teams = [...new Set(tasks.map(task => task.assignedTeam))];
    teams.forEach(team => {
      metrics.push(`${team} deliverables completed successfully`);
    });

    return metrics;
  }

  /**
   * Generate a response that delegates specific information gathering tasks to teams
   */
  private generateTeamDelegationResponse(qaResult: any): string {
    const responseText = qaResult.summary || qaResult.questions?.[0]?.answer || '';

    // Use the structured gap analysis if available
    const gapAnalysis = this.lastGapAnalysis;

    if (!gapAnalysis || gapAnalysis.identifiedGaps.length === 0) {
      // Fallback to generic delegation if no structured gaps identified
      return this.generateGenericDelegationResponse(responseText);
    }

    // Prepare delegation data for the new markdown formatter
    const delegationData = {
      status: "Information Gaps Identified",
      summary: responseText.substring(0, 500) + (responseText.length > 500 ? '...' : ''),
      urgencyLevel: "HIGH",
      totalGaps: gapAnalysis.totalGaps,
      identifiedGaps: gapAnalysis.identifiedGaps
    };

    // Use the new structured gap analysis markdown formatter
    return StrategicTaskFormatter.formatStructuredGapAnalysisMarkdown(delegationData);
  }

  /**
   * Generate generic delegation response when no structured gaps are identified
   */
  private generateGenericDelegationResponse(responseText: string): string {
    // Create generic gaps structure that matches the expected format
    const genericDelegationData = {
      status: "General Information Gaps Identified",
      summary: responseText.substring(0, 500) + (responseText.length > 500 ? '...' : ''),
      urgencyLevel: "HIGH",
      totalGaps: 4,
      identifiedGaps: [
        {
          category: "Market Intelligence",
          assignedTeam: "Research Team",
          priority: "HIGH",
          description: "Comprehensive market research and competitive analysis required",
          specificRequirements: [
            "Comprehensive market research and competitive analysis",
            "Target audience demographics and behavior analysis",
            "Industry trends and market dynamics assessment"
          ],
          timeline: "2 weeks",
          deliverable: "Market Research and Competitive Analysis Report"
        },
        {
          category: "Product Validation",
          assignedTeam: "Business Analysis Team",
          priority: "HIGH",
          description: "Product feature validation and documentation needed",
          specificRequirements: [
            "Product feature validation and documentation",
            "Performance metrics and benchmarking",
            "Customer success stories and case studies"
          ],
          timeline: "2 weeks",
          deliverable: "Product Validation and Metrics Report"
        },
        {
          category: "Customer Intelligence",
          assignedTeam: "Sales Team",
          priority: "MEDIUM",
          description: "Customer feedback collection and analysis required",
          specificRequirements: [
            "Customer feedback collection and analysis",
            "Sales channel effectiveness assessment",
            "Pricing sensitivity and competitive positioning"
          ],
          timeline: "2 weeks",
          deliverable: "Customer Intelligence Report"
        },
        {
          category: "Marketing Infrastructure",
          assignedTeam: "Marketing Team",
          priority: "MEDIUM",
          description: "Current marketing capabilities assessment needed",
          specificRequirements: [
            "Current marketing capabilities assessment",
            "Budget allocation and resource planning",
            "Brand guidelines and messaging consistency review"
          ],
          timeline: "1 week",
          deliverable: "Marketing Infrastructure Assessment"
        }
      ]
    };

    // Use the new structured gap analysis markdown formatter
    return StrategicTaskFormatter.formatStructuredGapAnalysisMarkdown(genericDelegationData);
  }

  /**
   * LLM-BASED: Generate intelligent information request response with specific team delegation
   * Analyzes QuestionAnswerAgent results to create targeted tasks for each identified information gap
   */
  private async generateInformationRequestResponse(qaResult: any): Promise<string> {
    console.log(`StrategicDirectorAgent: Generating LLM-based information request response`);

    try {
      // Extract information from qaResult
      const missingInfo = qaResult.summary || qaResult.questions?.[0]?.answer || 'Insufficient information to proceed with strategic analysis.';
      const questions = qaResult.questions || [];
      const additionalContext = qaResult.content || '';

      // Use Groq llama-3.3-70b-versatile for intelligent gap analysis and task generation
      const gapAnalysisPrompt = `
You are a Strategic Director analyzing insufficient information responses to create specific, actionable team delegation tasks.

QUESTION ANSWER AGENT RESULTS:
Summary: ${missingInfo}

Questions and Answers:
${questions.map((q: any, i: number) => `
Q${i + 1}: ${q.question || 'N/A'}
A${i + 1}: ${q.answer || 'N/A'}
`).join('\n')}

Additional Context: ${additionalContext}

TASK: Analyze the above results to identify SPECIFIC information gaps and create targeted tasks for appropriate teams.

AVAILABLE TEAMS:
- Research Team (market research, competitive analysis, data gathering, industry trends)
- Business Analysis Team (business processes, requirements, stakeholder analysis, metrics)
- Sales Team (customer insights, pricing, sales channels, market validation)
- Marketing Team (campaigns, branding, customer behavior, marketing channels)
- Software Design Team (technical requirements, implementation, user experience)
- Content Team (content strategy, documentation, communications)

RESPONSE FORMAT:
Return a JSON object with this structure:
{
  "status": "Information Gaps Identified",
  "summary": "Brief summary of the overall information gap situation",
  "identifiedGaps": [
    {
      "gapTitle": "Specific gap title",
      "gapDescription": "Detailed description of what information is missing",
      "assignedTeam": "Team Name",
      "priority": "HIGH|MEDIUM|LOW",
      "specificRequirements": ["requirement 1", "requirement 2", "requirement 3"],
      "deliverable": "What should be delivered",
      "timeline": "estimated duration (e.g., '1 week', '2 weeks')",
      "rationale": "Why this team is best suited for this task"
    }
  ],
  "totalGaps": "number of gaps identified",
  "urgencyLevel": "HIGH|MEDIUM|LOW",
  "nextSteps": ["step 1", "step 2", "step 3"]
}

CRITICAL REQUIREMENTS:
1. Create ONE task for EACH distinct information gap identified
2. Ensure 1:1 mapping between gaps and tasks
3. Assign teams based on task content and team expertise
4. Make requirements specific and actionable
5. Ensure deliverables are concrete and measurable
6. Set realistic timelines based on task complexity
7. Provide clear rationale for team assignments

TEAM ASSIGNMENT LOGIC:
- Market/competitive data → Research Team
- Business requirements/processes → Business Analysis Team
- Customer insights/sales data → Sales Team
- Marketing/branding questions → Marketing Team
- Technical/implementation → Software Design Team
- Content/documentation → Content Team

Return ONLY the JSON object, no additional text.
`;

      const llmResponse = await this.llmTool.processContent({
        prompt: gapAnalysisPrompt,
        model: 'llama-3.3-70b-versatile',
        provider: 'groq',
        modelOptions: {
          temperature: 0.2, // Low temperature for consistent analysis
          max_tokens: 3000
        }
      });

      console.log(`StrategicDirectorAgent: Received LLM response for gap analysis (${llmResponse.length} chars)`);

      // Parse the JSON response
      let gapAnalysis: any = {};
      try {
        // Try to extract JSON from the response
        const jsonMatch = llmResponse.match(/\{[\s\S]*\}/);
        if (jsonMatch) {
          gapAnalysis = JSON.parse(jsonMatch[0]);
        } else {
          // Fallback: try to parse the entire response as JSON
          gapAnalysis = JSON.parse(llmResponse);
        }
      } catch (parseError) {
        console.error(`StrategicDirectorAgent: Failed to parse LLM JSON response:`, parseError);
        console.log(`StrategicDirectorAgent: Raw LLM response:`, llmResponse.substring(0, 500));
        // Fall back to static response
        return this.generateStaticInformationRequestResponse(missingInfo);
      }

      console.log(`StrategicDirectorAgent: Successfully parsed gap analysis with ${gapAnalysis.identifiedGaps?.length || 0} gaps`);

      // Generate the formatted response
      return this.formatInformationRequestResponse(gapAnalysis);

    } catch (error) {
      console.error(`StrategicDirectorAgent: Error in LLM-based information request generation:`, error);
      console.log(`StrategicDirectorAgent: Falling back to static response`);

      // Fallback to static response
      const missingInfo = qaResult.summary || qaResult.questions?.[0]?.answer || 'Insufficient information to proceed with strategic analysis.';
      return this.generateStaticInformationRequestResponse(missingInfo);
    }
  }

  /**
   * Format the LLM-generated gap analysis into a structured response - ABSTRACTED TO UTILITY
   */
  private formatInformationRequestResponse(gapAnalysis: any): string {
    // Use the new markdown formatting utility for delegation structures
    return StrategicTaskFormatter.formatDelegationStructureMarkdown(gapAnalysis);
  }

  /**
   * Generate static fallback response when LLM processing fails - ABSTRACTED TO UTILITY
   */
  private generateStaticInformationRequestResponse(missingInfo: string): string {
    return StrategicTaskFormatter.generateStaticInformationRequestResponse(missingInfo);
  }

  /**
   * Generate comprehensive strategic analysis when sufficient information is available
   */
  async generateStrategicAnalysis(
    request: string,
    context: string | undefined
  ): Promise<string> {
    console.log(`StrategicDirectorAgent: Generating comprehensive strategic analysis`);

    const strategicAnalysisPrompt = `
    You are ${this.name}, a Strategic Director with comprehensive information to provide detailed strategic analysis.

    USER REQUEST:
    ${request}

    AVAILABLE CONTEXT:
    ${context || 'No additional context provided'}

    RECENT MEMORY CONTEXT:
    ${this.getRecentContext()}

    STRATEGIC ANALYSIS REQUIREMENTS:
    Provide a comprehensive strategic analysis that includes:

    1. **Strategic Reasoning** - Document your analysis process and frameworks applied
    2. **Situation Analysis** - Current state assessment and key insights
    3. **Strategic Recommendations** - Specific, actionable recommendations
    4. **Team Assignments** - Clear assignment of tasks to specific team members with rationale
    5. **Implementation Plan** - Detailed steps with timelines
    6. **Success Metrics** - KPIs and measurement framework
    7. **Risk Assessment** - Potential challenges and mitigation strategies

    TEAM ASSIGNMENT REQUIREMENTS:
    For each task or recommendation, you MUST clearly specify:
    - **Responsible Team Member**: Marketing Team, Research Team, Software Design Team, Sales Team, or Business Analysis Team
    - **Specific Role**: What exactly they will do
    - **Rationale**: Why this team member is best suited for this task
    - **Dependencies**: What they need from other team members
    - **Timeline**: When this should be completed

    EXAMPLE TEAM ASSIGNMENT FORMAT:
    "**Task**: Develop competitive analysis report
    **Assigned to**: Research Team
    **Rationale**: Research Team has specialized skills in market analysis and data gathering
    **Specific Role**: Conduct comprehensive competitor research, analyze market positioning, and create detailed comparison matrix
    **Dependencies**: Requires product specifications from Business Analysis Team
    **Timeline**: Complete within 2 weeks"

    RESPONSE FORMAT:
    - Use clear headings and structured sections
    - Include specific, actionable recommendations
    - Ensure every major task has clear team assignments
    - Maintain professional strategic director tone
    - Focus on practical implementation
    `;

    try {
      const response = await this.llmTool.processContent({
        prompt: strategicAnalysisPrompt,
        model: this.defaultLlmModel,
        provider: this.defaultLlmProvider,
        modelOptions: { temperature: 0.7 }
      });

      // Store the interaction in memory
      this.storeInteraction(request, response);

      // Generate a PDF document with the strategic analysis
      await this.generateStrategicAnalysisDocument(request, response);

      console.log(`StrategicDirectorAgent: Generated comprehensive strategic analysis`);
      return response;
    } catch (error) {
      console.error('StrategicDirectorAgent: Error generating strategic analysis:', error);
      return `I encountered an error while generating the strategic analysis. Please try again or provide additional context to help me better understand your requirements.`;
    }
  }

  /**
   * Generate a PDF document for strategic analysis
   */
  private async generateStrategicAnalysisDocument(request: string, response: string): Promise<void> {
    try {
      console.log(`StrategicDirectorAgent: Generating PDF document for strategic analysis`);

      // Create a strategy ID for the document
      const strategyId = `strategic-analysis-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;

      // Create a minimal strategy object for the document generation metadata
      const docStrategy: MarketingStrategy = {
        id: strategyId,
        name: `Strategic Analysis: ${request.substring(0, 50)}${request.length > 50 ? '...' : ''}`,
        description: request,
        objectives: ['Document comprehensive strategic analysis with team assignments'],
        targetAudience: {
          demographics: ['Strategic stakeholders'],
          psychographics: ['Decision makers'],
          behaviors: ['Strategic planning focused'],
          description: 'Internal stakeholders requiring strategic analysis documentation.'
        },
        valueProposition: 'Comprehensive strategic analysis with clear team assignments and implementation guidance',
        keyMessages: ['Strategic reasoning', 'Team assignments', 'Implementation plan'],
        channels: ['Internal Documentation'],
        timeline: {
          startDate: new Date(),
          endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
          description: 'Strategic analysis implementation timeline',
          milestones: [{
            name: 'Strategic Analysis Documentation',
            date: new Date(),
            description: 'Comprehensive strategic analysis with team assignments completed'
          }]
        },
        kpis: ['Strategic clarity', 'Team assignment clarity', 'Implementation feasibility'],
        createdAt: new Date(),
        updatedAt: new Date()
      };

      // Add to strategies array for document generation
      this.strategies.push(docStrategy);

      const pdfContents: PdfContent[] = [
        {
          title: docStrategy.name,
          content: `**Original Request:** ${request}\n\n**Analysis Date:** ${new Date().toLocaleDateString()}`
        },
        {
          title: 'Strategic Analysis with Team Assignments',
          content: response
        }
      ];

      const pdfOptions: PdfGenerationOptions = {
        title: docStrategy.name,
        subtitle: 'Strategic Analysis with Team Assignments',
        date: dateTimeTool.getCurrentDate({ format: 'medium', includeDayOfWeek: true }),
        saveToByteStore: true,
        agentId: this.id,
        agentName: this.name,
        category: 'Strategic Analysis',
        strategyId: docStrategy.id,
        documentType: 'strategic_analysis_with_teams'
      };

      // Generate PDF asynchronously
      this.pdfGeneratorTool.generatePdf(pdfContents, pdfOptions)
        .then(result => {
          console.log('Strategic analysis PDF with team assignments generated successfully');
          if (result && typeof result === 'object' && 'url' in result && result.url) {
            if (!this.memory.Agent_Response.documentUrls) {
              this.memory.Agent_Response.documentUrls = [];
            }
            this.memory.Agent_Response.documentUrls.push({
              request,
              url: result.url,
              timestamp: new Date(),
              type: 'strategic_analysis_with_teams'
            });
            this.saveMemoryToStorage();
          }
        })
        .catch(error => {
          console.error('Error generating strategic analysis PDF:', error);
        });
    } catch (error) {
      console.error('Error in strategic analysis PDF generation process:', error);
    }
  }

  /**
   * Create a marketing strategy
   */
  async createMarketingStrategy(
    name: string,
    description: string,
    objectives: string[],
    targetAudience: MarketingStrategy['targetAudience'],
    valueProposition: string,
    keyMessages: string[],
    channels: string[],
    timeline: MarketingStrategy['timeline'],
    kpis: string[]
  ): Promise<MarketingStrategy> {
    const strategy: MarketingStrategy = {
      id: `strategy-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
      name,
      description,
      objectives,
      targetAudience,
      valueProposition,
      keyMessages,
      channels,
      timeline,
      kpis,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    this.strategies.push(strategy);

    if (!this.memory.Agent_Response.strategies) {
      this.memory.Agent_Response.strategies = [];
    }
    this.memory.Agent_Response.strategies.push(strategy);
    await this.saveMemoryToStorage();

    return strategy;
  }

  /**
   * Analyze a product and identify its unique value propositions
   */
  async analyzeProduct(
    name: string,
    description: string,
    marketData: string
  ): Promise<ProductAnalysis> {
    const prompt = `
    Analyze the following product and identify its unique value propositions, competitive advantages,
    target market, and perform a SWOT analysis. Provide detailed explanations and insights for each section.

    Product Name: ${name}
    Product Description: ${description}
    Market Data: ${marketData}

    Please provide a comprehensive analysis with the following sections:
    1. Unique Selling Points - List and provide detailed explanation for each
    2. Competitive Advantages - Analyze how these advantages position the product in the market
    3. Target Market - Detailed description of ideal customer segments
    4. Pricing Strategy Recommendations - Include pricing psychology and competitive positioning
    5. Distribution Channel Recommendations - With rationale for each channel
    6. Market Position - Analysis of where this product fits in the overall market landscape
    7. Industry Trends - Relevant trends that may impact this product
    8. Customer Insights - Deep understanding of customer needs and pain points
    9. SWOT Analysis (Strengths, Weaknesses, Opportunities, Threats) - With detailed description
    10. Recommendations - Strategic recommendations based on the analysis
    11. Research Notes - Any additional insights or research findings

    IMPORTANT: Format your response strictly as a valid JSON object with these sections as keys. Follow these guidelines:
    - Use double quotes for all keys and string values.
    - Ensure all arrays have proper brackets and commas.
    - Avoid trailing commas in arrays and objects.
    - Ensure all opening brackets/braces have matching closing ones.
    - For string arrays, use simple strings without nested objects.
    - For the SWOT analysis, use the format: { "strengths": [...], "weaknesses": [...], "opportunities": [...], "threats": [...], "description": "..." }

    Begin your response with the JSON object and nothing else. Do not include any explanatory text before or after the JSON.
    `;

    const modelOptions = { temperature: 0.2 };
    const analysisJson = await this.llmTool.processContent({
      prompt,
      model: this.defaultLlmModel,
      provider: this.defaultLlmProvider,
      modelOptions,
    });

    try {
      const validatedData = parseProductAnalysisResponse(analysisJson);
      const productAnalysis: ProductAnalysis = {
        id: `product-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
        name,
        description,
        uniqueSellingPoints: validatedData.uniqueSellingPoints,
        competitiveAdvantages: validatedData.competitiveAdvantages,
        targetMarket: validatedData.targetMarket,
        pricingStrategy: validatedData.pricingStrategy,
        distributionChannels: validatedData.distributionChannels,
        marketPosition: validatedData.marketPosition,
        industryTrends: validatedData.industryTrends,
        customerInsights: validatedData.customerInsights,
        swotAnalysis: {
          strengths: validatedData.swotAnalysis.strengths,
          weaknesses: validatedData.swotAnalysis.weaknesses,
          opportunities: validatedData.swotAnalysis.opportunities,
          threats: validatedData.swotAnalysis.threats,
          description: validatedData.swotAnalysis.description
        },
        recommendations: validatedData.recommendations,
        researchNotes: validatedData.researchNotes,
        createdAt: new Date()
      };

      this.productAnalyses.push(productAnalysis);

      if (!this.memory.Agent_Response.productAnalyses) {
        this.memory.Agent_Response.productAnalyses = [];
      }
      this.memory.Agent_Response.productAnalyses.push(productAnalysis);
      await this.saveMemoryToStorage();

      return productAnalysis;
    } catch (error) {
      console.error('Error processing product analysis:', error);
      throw new Error('Failed to process product analysis response');
    }
  }

  /**
   * Generate a marketing strategy based on product analysis and market research
   * COMMENTED OUT TO REDUCE COMPLEXITY
   */
  /*
  async generateMarketingStrategy(
    productId: string,
    marketResearch: string,
    businessObjectives: string[]
  ): Promise<MarketingStrategy> {
    const productAnalysis = this.productAnalyses.find(p => p.id === productId);

    if (!productAnalysis) {
      throw new Error(`Product analysis not found for ID: ${productId}`);
    }

    try {
      // Step 1: Query documents for relevant marketing strategies and case studies
      console.log(`StrategicDirectorAgent: Querying for relevant marketing strategies for ${productAnalysis.name}`);
      const queryResult = await this.queryDocuments( // UPDATED to use the new queryDocuments
        `Find marketing strategies and case studies relevant to: ${productAnalysis.name}, ${productAnalysis.uniqueSellingPoints.join(', ')}, ${businessObjectives.join(', ')}`,
        this.userId, // Pass userId
        'marketing-strategies', // category
        undefined, // filename
        false // useInternetSearch by default
      );

      // Step 2: Use LLM to analyze the market and competition based on constructed context
      console.log(`StrategicDirectorAgent: Analyzing market and competition for ${productAnalysis.name}`);
      const marketAnalysisRequest = `Analyze the market and competition for ${productAnalysis.name} with these unique selling points: ${productAnalysis.uniqueSellingPoints.join(', ')}`;
      const marketAnalysisContext = `
        Product Description: ${productAnalysis.description}
        Target Market: ${productAnalysis.targetMarket.join(', ')}
        Market Position: ${productAnalysis.marketPosition || 'Not specified'}
        Industry Trends: ${productAnalysis.industryTrends || 'Not specified'}
        Market Research: ${marketResearch}
        SWOT Analysis:
        - Strengths: ${productAnalysis.swotAnalysis.strengths.join(', ')}
        - Weaknesses: ${productAnalysis.swotAnalysis.weaknesses.join(', ')}
        - Opportunities: ${productAnalysis.swotAnalysis.opportunities.join(', ')}
        - Threats: ${productAnalysis.swotAnalysis.threats.join(', ')}
        ${productAnalysis.swotAnalysis.description ? `- Analysis: ${productAnalysis.swotAnalysis.description}` : ''}
        ${queryResult.success ? `Relevant Case Studies: ${queryResult.content}` : ''}
      `;

      const marketAnalysisExtractionPrompt = `
      You are a strategic marketing director analyzing the market and competition for ${productAnalysis.name}.
      Your task is to provide a comprehensive market and competition analysis based on the provided context.

      CONTEXT INFORMATION:
      """
      ${marketAnalysisContext}
      """

      User Request: "${marketAnalysisRequest}"

      Please provide a comprehensive market and competition analysis that includes:
      1. Market Overview - Current state of the market
      2. Competitive Landscape - Key competitors and their positions
      3. Competitive Advantages - How ${productAnalysis.name} can differentiate
      4. Market Opportunities - Potential growth areas
      5. Market Threats - Challenges and obstacles
      6. Strategic Recommendations - How to position in the market

      Your analysis should be insightful and directly address how ${productAnalysis.name} can compete effectively.
      `;

      const marketAnalysisText = await this.llmTool.processContent({
          prompt: marketAnalysisExtractionPrompt,
          model: this.defaultLlmModel,
          provider: this.defaultLlmProvider,
          modelOptions: { temperature: 0.3, max_tokens: 1500 }
      });

      const marketAnalysisResult: QuestionAnswerResult = {
        success: true,
        questions: [{
          question: marketAnalysisRequest,
          answer: marketAnalysisText
        }],
        summary: marketAnalysisText,
        toolsUsed: ['llmTool'] // Updated tool usage
      };

      // Step 3: Create an enhanced prompt with the additional insights
      const enhancedMarketResearch = marketAnalysisResult.success && marketAnalysisResult.summary
        ? `${marketResearch}\n\nAdditional Market Analysis: ${marketAnalysisResult.summary}`
        : marketResearch;

      // Create a prompt for strategy generation with enhanced insights
      const prompt = `
      Generate a comprehensive marketing strategy based on the following product analysis and market research.
      Provide detailed explanations and strategic thinking for each component.

      Product Name: ${productAnalysis.name}
      Product Description: ${productAnalysis.description}

      Unique Selling Points: ${productAnalysis.uniqueSellingPoints.join(', ')}
      Competitive Advantages: ${productAnalysis.competitiveAdvantages.join(', ')}
      Target Market: ${productAnalysis.targetMarket.join(', ')}
      Market Position: ${productAnalysis.marketPosition || 'Not specified'}
      Industry Trends: ${productAnalysis.industryTrends || 'Not specified'}

      SWOT Analysis:
      - Strengths: ${productAnalysis.swotAnalysis.strengths.join(', ')}
      - Weaknesses: ${productAnalysis.swotAnalysis.weaknesses.join(', ')}
      - Opportunities: ${productAnalysis.swotAnalysis.opportunities.join(', ')}
      - Threats: ${productAnalysis.swotAnalysis.threats.join(', ')}
      ${productAnalysis.swotAnalysis.description ? `- Analysis: ${productAnalysis.swotAnalysis.description}` : ''}

      Market Research: ${enhancedMarketResearch}

      Business Objectives: ${businessObjectives.join(', ')}

      ${queryResult.success ? `Relevant Case Studies and Strategies: ${queryResult.content}` : ''}

      ${marketAnalysisResult.success && marketAnalysisResult.questions ? `
      Market Analysis Questions and Answers:
      ${marketAnalysisResult.questions.map((qa: any, i: number) => `
      Q${i+1}: ${qa.question}
      A${i+1}: ${qa.answer}
      `).join('\n')}
      ` : ''}

      Please generate a detailed marketing strategy with the following components:
      1. Strategy Name
      2. Strategy Description
      3. Executive Summary - High-level overview of the strategy
      4. Marketing Objectives - Specific, measurable objectives
      5. Target Audience - Detailed analysis of demographics, psychographics, behaviors, pain points, and buying journey
      6. Value Proposition - Clear articulation with detailed explanation
      7. Key Messages - With rationale for each message
      8. Marketing Channels - With detailed strategy for each channel
      9. Content Strategy - Approach to content creation and distribution
      10. Budget - Allocation recommendations with breakdown
      11. Timeline - 3-month plan with key milestones and detailed implementation plan
      12. KPIs - Specific metrics and measurement framework
      13. Risk Assessment - Potential risks and mitigation strategies
      14. Competitive Positioning - Strategy relative to competitors
      15. Marketing Research - Summary of research that informs the strategy
      16. Conceptual Framework - Theoretical marketing framework that applies to this strategy

      IMPORTANT: Format your response strictly as a valid JSON object with these sections as keys. Follow these guidelines:
      - Use double quotes for all keys and string values.
      - Ensure all arrays have proper brackets and commas.
      - Avoid trailing commas in arrays and objects.
      - Ensure all opening brackets/braces have matching closing ones.
      - For string arrays, use simple strings without nested objects.
      - For the timeline, use the format: { "milestones": [{ "name": "...", "date": "YYYY-MM-DD", "description": "..." }], "description": "..." }
      - For the budget, use the format: { "total": number, "breakdown": { "category1": number, "category2": number }, "notes": "..." }
      - For the risk assessment, use the format: [{ "risk": "...", "impact": "...", "mitigation": "..." }]

      Begin your response with the JSON object and nothing else. Do not include any explanatory text before or after the JSON.
      `;

      const modelOptions = { temperature: 0.2 };
      const strategyJson = await this.llmTool.processContent({
        prompt,
        model: this.defaultLlmModel,
        provider: this.defaultLlmProvider,
        modelOptions,
      });

      // Parse and validate the response using Zod schema
      const validatedData = parseMarketingStrategyResponse(strategyJson); // parseMarketingStrategyResponse needs to be defined or re-enabled

      // Process milestones to convert string dates to Date objects
      const processedMilestones = validatedData.timeline.milestones.map(milestone => ({
        name: milestone.name,
        date: new Date(milestone.date),
        description: milestone.description
      }));

      const strategy: MarketingStrategy = {
        id: `strategy-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
        name: validatedData.name || `${productAnalysis.name} Marketing Strategy`,
        description: validatedData.description,
        executiveSummary: validatedData.executiveSummary,
        objectives: validatedData.objectives,
        targetAudience: {
          demographics: validatedData.targetAudience.demographics,
          psychographics: validatedData.targetAudience.psychographics,
          behaviors: validatedData.targetAudience.behaviors,
          description: validatedData.targetAudience.description,
          painPoints: validatedData.targetAudience.painPoints,
          buyingJourney: validatedData.targetAudience.buyingJourney,
          segmentDetails: validatedData.targetAudience.segmentDetails
        },
        valueProposition: validatedData.valueProposition,
        keyMessages: validatedData.keyMessages,
        channels: validatedData.channels,
        channelStrategy: validatedData.channelStrategy,
        contentStrategy: validatedData.contentStrategy,
        timeline: {
          startDate: new Date(),
          endDate: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000),
          description: validatedData.timeline.description,
          milestones: processedMilestones
        },
        budget: validatedData.budget,
        kpis: validatedData.kpis,
        riskAssessment: validatedData.riskAssessment,
        competitiveAnalysis: validatedData.competitiveAnalysis,
        marketingResearch: validatedData.marketingResearch,
        conceptualFramework: validatedData.conceptualFramework,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      this.strategies.push(strategy);

      if (!this.memory.longTerm.strategies) {
        this.memory.longTerm.strategies = [];
      }
      this.memory.longTerm.strategies.push(strategy);
      await this.saveMemoryToStorage();

      return strategy;
    } catch (error) {
      console.error('Error processing marketing strategy:', error);
      throw new Error('Failed to process marketing strategy response');
    }
  }
  */

  /**
   * Generate a long-form document on any marketing topic
   */
  async generateLongFormDocument(topic: string, format: string = 'markdown', additionalInstructions: string = ''): Promise<string> {
    const prompt = `
    Generate a comprehensive, detailed document on the following marketing topic.
    Provide in-depth analysis, insights, and strategic thinking.

    Topic: ${topic}
    Format: ${format}
    ${additionalInstructions ? `Additional Instructions: ${additionalInstructions}` : ''}

    Please create a well-structured document with appropriate sections, headings, and formatting.
    Focus on providing valuable insights and actionable information.
    Include relevant examples, case studies, and best practices where appropriate.

    DO NOT format your response as JSON. Provide a natural, well-formatted document in ${format}.
    `;

    const modelOptions = { temperature: 0.7 };
    const documentContent = await this.llmTool.processContent({
        prompt,
        model: this.defaultLlmModel,
        provider: this.defaultLlmProvider,
        modelOptions
    });

    if (!this.memory.Agent_Response.documents) {
      this.memory.Agent_Response.documents = [];
    }
    const document = {
      id: `doc-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
      topic,
      format,
      content: documentContent,
      createdAt: new Date(),
      formattedDate: dateTimeTool.getCurrentDateTime({ format: 'full' })
    };
    this.memory.Agent_Response.documents.push(document);
    await this.saveMemoryToStorage();
    return documentContent;
  }

  /**
   * Develop brand strategy and guidelines
   */
  async developBrandStrategy(brandName: string, description: string, marketContext: string = '', targetAudience: any = {}): Promise<any> {
    const prompt = `
    Develop a comprehensive brand strategy and guidelines for the following brand. Provide detailed analysis, strategic direction, and implementation guidelines.

    Brand Name: ${brandName}
    Brand Description: ${description}
    Market Context: ${marketContext}
    Target Audience: ${JSON.stringify(targetAudience)}

    Please provide a detailed brand strategy with the following sections:
    1. Brand Essence - Core identity and values
    2. Brand Positioning - Unique position in the market
    3. Brand Personality - Character traits and tone
    4. Brand Promise - What the brand commits to deliver
    5. Brand Messaging - Key messages and communication strategy
    6. Brand Visual Identity - Guidelines for visual elements (describe conceptually)
    7. Brand Voice - Tone and language guidelines
    8. Brand Experience - How customers interact with the brand
    9. Brand Architecture - Structure and relationship between brand elements
    10. Brand Metrics - How to measure brand performance
    11. Brand Implementation - How to roll out and maintain the brand

    IMPORTANT: Format your response strictly as a valid JSON object with these sections as keys. Follow these guidelines:
    - Use double quotes for all keys and string values.
    - Ensure all arrays have proper brackets and commas.
    - Avoid trailing commas in arrays and objects.
    - Ensure all opening brackets/braces have matching closing ones.
    - For string arrays, use simple strings without nested objects.

    Begin your response with the JSON object and nothing else. Do not include any explanatory text before or after the JSON.
    `;

    const modelOptions = { temperature: 0.2 };
    const brandJson = await this.llmTool.processContent({
        prompt,
        model: this.defaultLlmModel,
        provider: this.defaultLlmProvider,
        modelOptions
    });

    try {
      const extractedJson = extractJsonFromText(brandJson);
      console.log('Extracted JSON from brand strategy:', extractedJson.substring(0, 100) + '...');
      const brandData = JSON.parse(extractedJson);

      if (!this.memory.Agent_Response.brandStrategies) {
        this.memory.Agent_Response.brandStrategies = [];
      }
      const brandStrategy = {
        id: `brand-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
        name: brandName,
        description,
        marketContext,
        targetAudience,
        strategy: brandData,
        createdAt: new Date()
      };
      this.memory.Agent_Response.brandStrategies.push(brandStrategy);
      await this.saveMemoryToStorage();
      return brandData;
    } catch (error) {
      console.error('Error processing brand strategy:', error);
      return {
        success: false,
        error: 'Failed to parse brand strategy JSON response',
        rawResponse: brandJson
      };
    }
  }

  // Helper to extract model name - ABSTRACTED TO UTILITY
  private extractModelName(modelNameOrProvider?: string): string | undefined {
    return StrategicTaskUtils.extractModelName(modelNameOrProvider);
  }

  /**
   * Query documents using the new QueryDocumentsAgent.
   * This replaces the old queryDocuments and queryDocumentsEnhanced methods.
   *
   * @param query - The search query
   * @param userId - The user ID (defaults to this.userId)
   * @param category - Optional document category to search within
   * @param filename - Optional specific filename to search within
   * @param useInternetSearch - Whether to use internet search as fallback
   * @param modelNameOrProvider - Optional model string (e.g., "gpt-4o" or "openai/gpt-4o")
   *                            The QueryDocumentsAgent will use this for its internal tools if applicable.
   * @returns Query results with content and metadata
   */
  async queryDocuments(
    query: string,
    userId: string = this.userId,
    category?: string,
    filename?: string,
    useInternetSearch: boolean = false,
    modelNameOrProvider?: string
  ): Promise<QueryDocumentsAgentResult> {
    console.log(`StrategicDirectorAgent: Delegating document query to QueryDocumentsAgent for "${query}"`);

    try {
      // The QueryDocumentsAgent's `process` method expects `model` (name) and `modelOptions`.
      // We'll extract the model name. The provider part is trickier as QDA uses specific internal models
      // for some tasks, but its tools might accept provider if they use a generic LLM utility.
      // For now, we pass the model name and let QDA handle its internal logic.
      const modelName = this.extractModelName(modelNameOrProvider);

      const result = await this.queryDocumentsAgent.process({
        query,
        userId, // Use the provided userId or the agent's userId
        category,
        filename,
        useInternetSearch,
        model: modelName, // Pass the extracted model name
        // modelOptions: If specific options are tied to the provider, this needs more complex handling.
        // For now, let QDA use its default modelOptions or those passed if any.
      });

      // Store the query and result in memory for context
      if (!this.memory.Agent_Response.documentQueries) {
        this.memory.Agent_Response.documentQueries = [];
      }
      this.memory.Agent_Response.documentQueries.push({
        query,
        result: {
          success: result.success,
          content: (result.content || "").substring(0, 500) + ((result.content || "").length > 500 ? '...' : ''),
          timestamp: new Date()
        }
      });
      if (this.memory.Agent_Response.documentQueries.length > 10) {
        this.memory.Agent_Response.documentQueries = this.memory.Agent_Response.documentQueries.slice(-10);
      }
      await this.saveMemoryToStorage();

      return result;

    } catch (error) {
      console.error(`Error in StrategicDirectorAgent querying documents via QueryDocumentsAgent:`, error);
      return {
        success: false,
        content: '',
        error: error instanceof Error ? error.message : 'Unknown error occurred during document query delegation',
        // Ensure metadata and other optional fields are present if the type requires them for error cases
        followUpQuestions: [],
        relatedCategories: [],
        matchingDocuments: [],
        sources: [],
        metadata: { internetSearchUsed: false, functionCallingUsed: false }
      };
    }
  }

  /**
   * Enhanced document querying method for backward compatibility with existing API routes.
   * This method delegates to the new queryDocuments method which uses QueryDocumentsAgent.
   *
   * @param query - The search query
   * @param category - Optional document category to search within
   * @param filename - Optional specific filename to search within
   * @param namespace - Optional namespace (ignored, kept for backward compatibility)
   * @param useInternetSearch - Whether to use internet search as fallback
   * @param modelNameOrProvider - Optional model string (e.g., "gpt-4o" or "openai/gpt-4o")
   * @returns Query results with content and metadata
   */
  async queryDocumentsEnhanced(
    query: string,
    category?: string,
    filename?: string,
    _namespace?: string, // Ignored, kept for backward compatibility
    useInternetSearch: boolean = false,
    modelNameOrProvider?: string
  ): Promise<QueryDocumentsAgentResult> {
    console.log(`StrategicDirectorAgent: queryDocumentsEnhanced called, delegating to queryDocuments for "${query}"`);

    // Ensure QueryDocumentsAgent is initialized
    if (!this.queryDocumentsAgent) {
      console.log(`StrategicDirectorAgent: Initializing QueryDocumentsAgent on demand`);
      this.queryDocumentsAgent = new QueryDocumentsAgent({
        maxResults: 2,
        defaultTemperature: 0.3,
        defaultMaxTokens: 3000
      });
    }

    try {
      // Delegate to the queryDocuments method
      return await this.queryDocuments(
        query,
        this.userId,
        category,
        filename,
        useInternetSearch,
        modelNameOrProvider
      );
    } catch (error) {
      console.error(`StrategicDirectorAgent: Error in queryDocumentsEnhanced:`, error);
      // Return a fallback result in case of error
      return {
        success: false,
        content: '',
        error: error instanceof Error ? error.message : 'Unknown error occurred during document query',
        followUpQuestions: [],
        relatedCategories: [],
        matchingDocuments: [],
        sources: [],
        metadata: { internetSearchUsed: false, functionCallingUsed: false }
      };
    }
  }

  /**
   * Retrieve PMO tasks and assessment details using vector search
   * This method searches for PMO-related tasks and requirements to provide complete context
   * for marketing strategy development.
   *
   * @param pmoId - The PMO record ID to search for
   * @param userRequest - The user's marketing request for context
   * @param modelNameOrProvider - Optional model string for the search
   * @returns PMO task retrieval result with tasks and assessment details
   */
  async retrievePMOTasks(
    pmoId: string,
    userRequest: string,
    modelNameOrProvider?: string
  ): Promise<{
    success: boolean;
    tasks: string[];
    assessment: string;
    requirements: string;
    error?: string;
  }> {
    console.log(`StrategicDirectorAgent: Retrieving PMO tasks for PMO ID: ${pmoId}`);

    try {
      // Import Firebase admin for PMO record access
      const { adminDb } = await import('../../../components/firebase-admin');

      // First, try to get the PMO record directly from Firestore using the correct path
      let pmoRecord: any = null;
      try {
        // PMO records are stored in users/{userId}/PMO/{pmoId}
        const pmoDoc = await adminDb.collection('users').doc(this.userId).collection('PMO').doc(pmoId).get();
        if (pmoDoc.exists) {
          pmoRecord = { id: pmoDoc.id, ...pmoDoc.data() };
          console.log(`StrategicDirectorAgent: Found PMO record: "${pmoRecord.title}" for PMO ID: ${pmoId}`);
          console.log(`StrategicDirectorAgent: PMO record details - Description: "${pmoRecord.description?.substring(0, 200)}..."`);
        } else {
          console.log(`StrategicDirectorAgent: PMO record not found at users/${this.userId}/PMO/${pmoId}`);
        }
      } catch (pmoError) {
        console.warn(`StrategicDirectorAgent: Could not fetch PMO record directly:`, pmoError);
      }

      // Search for PMO-related documents using a more targeted approach
      // First, try to find documents that have this specific PMO ID in their metadata
      let searchResult: any = { success: false, content: '' };
      const pmoTitle = pmoRecord?.title || '';

      try {
        console.log(`StrategicDirectorAgent: Searching for documents with PMO ID: ${pmoId}`);

        // Get all files that have this PMO ID
        const filesQuery = adminDb.collection('users')
          .doc(this.userId)
          .collection('files')
          .where('pmoId', '==', pmoId);

        const filesSnapshot = await filesQuery.get();
        console.log(`StrategicDirectorAgent: Found ${filesSnapshot.docs.length} files with PMO ID ${pmoId}`);

        if (!filesSnapshot.empty) {
          // Get the namespaces (document IDs) for these files
          const pmoDocumentNamespaces = filesSnapshot.docs.map(doc => {
            const data = doc.data();
            console.log(`StrategicDirectorAgent: Found PMO document - Title: "${data.title}", Namespace: "${data.namespace}"`);
            return data.namespace;
          }).filter(namespace => namespace);

          if (pmoDocumentNamespaces.length > 0) {
            console.log(`StrategicDirectorAgent: Searching in specific PMO document namespaces: ${pmoDocumentNamespaces.join(', ')}`);

            // Use the ContentSelector to search in these specific namespaces
            const { ContentSelector } = await import('../../../app/src/processContent/selectContent');
            const { getEmbedding } = await import('../../tools/embedding-tool');

            const searchQuery = `PMO requirements tasks assessment ${pmoId} ${pmoTitle} ${userRequest}`;
            const queryEmbedding = await getEmbedding(searchQuery);

            const contentSelector = new ContentSelector(this.userId);

            // Create a simple token manager for the content selection
            // Use a minimal token manager similar to what's used in genericDocumentContentExtractorTool
            const minimalTokenManager = {
              getTokenConfig: () => ({ maxTokens: 8000, reservedTokens: 1000, model: 'text-embedding-ada-002' }),
              trackTokenUsage: () => {},
              getRemainingTokens: () => 7000
            };

            const contentResult = await contentSelector.selectContent(
              queryEmbedding,
              pmoDocumentNamespaces,
              minimalTokenManager as any
            );

            if (contentResult && contentResult.content) {
              searchResult = {
                success: true,
                content: contentResult.content,
                metadata: contentResult.metadata
              };
              console.log(`StrategicDirectorAgent: Successfully retrieved PMO content from specific namespaces (${contentResult.content.length} chars)`);
            } else {
              console.log(`StrategicDirectorAgent: No content found in PMO document namespaces`);
            }
          }
        }
      } catch (namespaceError) {
        console.warn(`StrategicDirectorAgent: Error searching specific PMO namespaces:`, namespaceError);
      }

      // If namespace-specific search didn't work, fall back to category search
      if (!searchResult.success || !searchResult.content) {
        console.log(`StrategicDirectorAgent: Falling back to category-based search`);

        const pmoSearchQuery = `PMO requirements tasks assessment ${pmoId} ${pmoTitle} ${userRequest}`;

        searchResult = await this.queryDocuments(
          pmoSearchQuery,
          this.userId,
          'PMO', // Search in PMO category
          undefined,
          false, // Don't use internet search for internal PMO data
          modelNameOrProvider
        );
      }

      // Log the content preview to verify we're getting the right PMO content
      if (searchResult.success && searchResult.content) {
        const contentPreview = searchResult.content.substring(0, 300);
        console.log(`StrategicDirectorAgent: Retrieved PMO content preview: "${contentPreview}..."`);

        // Check if the content contains the PMO ID to verify it's the correct document
        if (searchResult.content.includes(pmoId)) {
          console.log(`StrategicDirectorAgent: ✓ Content contains PMO ID ${pmoId} - correct document retrieved`);
        } else {
          console.warn(`StrategicDirectorAgent: ⚠ Content does not contain PMO ID ${pmoId} - may be wrong document`);
        }

        // Check if the content contains the PMO title to further verify
        if (pmoTitle && searchResult.content.toLowerCase().includes(pmoTitle.toLowerCase())) {
          console.log(`StrategicDirectorAgent: ✓ Content contains PMO title "${pmoTitle}" - correct document verified`);
        } else if (pmoTitle) {
          console.warn(`StrategicDirectorAgent: ⚠ Content does not contain PMO title "${pmoTitle}" - may be wrong document`);
        }
      }

      let tasks: string[] = [];
      let assessment = '';
      let requirements = '';

      // Extract information from the search results
      if (searchResult.success && searchResult.content) {
        console.log(`StrategicDirectorAgent: Found PMO content (${searchResult.content.length} characters)`);

        // Use LLM to extract structured task information
        const extractionPrompt = `
        You are analyzing PMO (Project Management Office) documentation to extract specific task and requirement information.

        PMO Content:
        ${searchResult.content}

        User Request Context:
        ${userRequest}

        Please extract and structure the following information in JSON format:
        {
          "tasks": ["list of specific tasks identified in the PMO documentation"],
          "assessment": "summary of the PMO assessment and analysis",
          "requirements": "detailed requirements and specifications from the PMO"
        }

        Focus on:
        1. Specific actionable tasks mentioned in the PMO documentation
        2. Assessment details that would inform marketing strategy
        3. Requirements that the marketing team should consider

        If no specific information is found, return empty arrays/strings but maintain the JSON structure.
        `;

        const extractionResult = await this.llmTool.processContent({
          prompt: extractionPrompt,
          model: this.defaultLlmModel,
          provider: this.defaultLlmProvider,
          modelOptions: { temperature: 0.3, max_tokens: 2000 }
        });

        try {
          const extractedJson = extractJsonFromText(extractionResult);
          const extractedData = JSON.parse(extractedJson);

          tasks = Array.isArray(extractedData.tasks) ? extractedData.tasks : [];
          assessment = extractedData.assessment || '';
          requirements = extractedData.requirements || '';

          console.log(`StrategicDirectorAgent: Extracted ${tasks.length} tasks from PMO content`);
        } catch (parseError) {
          console.warn(`StrategicDirectorAgent: Could not parse extraction result, using raw content:`, parseError);
          assessment = searchResult.content.substring(0, 1000);
        }
      }

      // If we have a PMO record but no search results, extract from the record
      if (pmoRecord && (!searchResult.success || !searchResult.content)) {
        console.log(`StrategicDirectorAgent: Using PMO record data as fallback`);

        assessment = pmoRecord.summary || pmoRecord.description || '';
        requirements = pmoRecord.customContext || '';

        // Try to extract tasks from the PMO record description or summary
        if (pmoRecord.summary || pmoRecord.description) {
          const taskExtractionPrompt = `
          Extract specific tasks from this PMO description:
          ${pmoRecord.summary || pmoRecord.description}

          Return a JSON array of task strings: ["task1", "task2", ...]
          `;

          try {
            const taskResult = await this.llmTool.processContent({
              prompt: taskExtractionPrompt,
              model: this.defaultLlmModel,
              provider: this.defaultLlmProvider,
              modelOptions: { temperature: 0.3, max_tokens: 1000 }
            });

            const taskJson = extractJsonFromText(taskResult);
            const extractedTasks = JSON.parse(taskJson);
            if (Array.isArray(extractedTasks)) {
              tasks = extractedTasks;
            }
          } catch (taskError) {
            console.warn(`StrategicDirectorAgent: Could not extract tasks from PMO record:`, taskError);
          }
        }
      }

      return {
        success: true,
        tasks,
        assessment,
        requirements
      };

    } catch (error) {
      console.error(`StrategicDirectorAgent: Error retrieving PMO tasks:`, error);
      return {
        success: false,
        tasks: [],
        assessment: '',
        requirements: '',
        error: error instanceof Error ? error.message : 'Unknown error occurred during PMO task retrieval'
      };
    }
  }


  /**
   * Process a complex question. This version uses the LLM tool directly with provided context.
   * If document querying is needed before answering, call `this.queryDocuments` first.
   *
   * @param request - The question to answer
   * @param context - Optional string context to use for answering
   * @param _category - Unused, kept for signature compatibility if needed later
   * @param _enabledTools - Unused
   * @param modelInfo - Optional model provider and name for the LLM call
   * @returns Question answer result
   */
  async answerQuestion(
    request: string,
    context?: string,
    _category?: string,
    _enabledTools: {
      internetSearch?: boolean;
      calculator?: boolean;
      calendar?: boolean;
    } = {},
    modelInfo?: { modelProvider: LlmProvider, modelName: string }
  ): Promise<QuestionAnswerResult> {
    console.log(`StrategicDirectorAgent: Answering question "${request}"`);

    try {
      let processedContext = context || 'No context provided.';

      // If context is substantial, use LLM to extract relevant parts or summarize.
      // This replaces the direct call to genericDocumentContentExtractorTool on a string.
      if (context && context.length > 300) { // Arbitrary length to trigger processing
        const contextProcessingPrompt = `
        Given the following text, extract the information most relevant to answering the question: "${request}".
        Provide a concise summary of these relevant points.

        Context Text:
        """
        ${context}
        """

        Relevant Information Summary:
        `;
        processedContext = await this.llmTool.processContent({
            prompt: contextProcessingPrompt,
            model: modelInfo?.modelName || this.defaultLlmModel,
            provider: modelInfo?.modelProvider || this.defaultLlmProvider,
            modelOptions: { temperature: 0.1, max_tokens: Math.min(1000, Math.floor(context.length / 2)) }
        });
        console.log(`StrategicDirectorAgent: Processed context for question answering.`);
      }

      const prompt = `
      You are a strategic marketing director. Your task is to answer the following question based on the provided context.

      QUESTION:
      ${request}

      AVAILABLE CONTEXT:
      ${processedContext}

      INSTRUCTIONS:
      1. Carefully review the question and the context.
      2. Formulate a comprehensive and insightful answer to the question using ONLY the information available in the context.
      3. If the context does not contain sufficient information to fully answer the question, clearly state this limitation in your answer.
      4. Structure your answer logically.
      5. Ensure your response is professional and aligns with the role of a Strategic Director.

      ANSWER:
      `;

      const answer = await this.llmTool.processContent({
        prompt,
        model: modelInfo?.modelName || this.defaultLlmModel,
        provider: modelInfo?.modelProvider || this.defaultLlmProvider,
        modelOptions: { temperature: 0.3 }
      });

      const result: QuestionAnswerResult = {
        success: true,
        questions: [{
          question: request,
          answer: answer
        }],
        summary: answer, // Ensure summary is always provided
        toolsUsed: ['llmTool'] // Tool usage updated
      };

      // Store the question and result in memory for context
      if (!this.memory.Agent_Response.questionAnswers) {
        this.memory.Agent_Response.questionAnswers = [];
      }
      this.memory.Agent_Response.questionAnswers.push({
        request,
        result: {
          success: result.success,
          summary: result.summary,
          timestamp: new Date()
        }
      });
      if (this.memory.Agent_Response.questionAnswers.length > 10) {
        this.memory.Agent_Response.questionAnswers = this.memory.Agent_Response.questionAnswers.slice(-10);
      }
      await this.saveMemoryToStorage();

      return result;
    } catch (error) {
      console.error('Error answering question:', error);
      return {
        success: false,
        questions: [],
        summary: '', // Ensure summary is part of the error response type
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }


  /**
   * Detect if a request is research-related and should be delegated to the ResearchInsightsAgent
   */
  private async detectResearchRequest(prompt: string): Promise<boolean> {
    console.log(`[StrategicDirectorAgent] Analyzing if request is research-related: "${prompt.substring(0, 100)}${prompt.length > 100 ? '...' : ''}"`);
    const detectionPrompt = `
    You are analyzing a user request to determine if it's primarily a research-related request that should be handled by the Research & Insights Agent.

    RESEARCH-RELATED requests are those that explicitly ask for data gathering, analysis, or investigation:
    - "Research the market for..."
    - "Analyze competitors of..."
    - "What are the current trends in..."
    - "Conduct market research on..."
    - "Find data about..."
    - "Investigate consumer behavior for..."
    - "Study the industry landscape of..."

    NOT RESEARCH-RELATED requests are creative, strategic, or campaign development tasks:
    - "Develop a campaign for..."
    - "Create a video concept for..."
    - "Design a marketing strategy for..."
    - "Build a brand identity for..."
    - "Generate creative ideas for..."
    - "Develop content for..."

    Key distinction: Research requests ask for INVESTIGATION and DATA GATHERING, while creative requests ask for DEVELOPMENT and CREATION of marketing materials/strategies.

    User Request: "${prompt}"

    Is this primarily asking for research/investigation/data gathering? Respond with only "yes" or "no".
    `;
    const modelOptions = { temperature: 0.1 };
    const response = await this.llmTool.processContent({
      prompt: detectionPrompt,
      model: this.defaultLlmModel,
      provider: this.defaultLlmProvider,
      modelOptions
    });
    const normalizedResponse = response.toLowerCase().trim();
    const isResearchRequest = normalizedResponse.includes('yes');
    console.log(`[StrategicDirectorAgent] Request classified as ${isResearchRequest ? 'RESEARCH-RELATED' : 'NOT research-related'}`);
    return isResearchRequest;
  }

  /**
   * Handle a research-related request by delegating to the ResearchInsightsAgent
   */
  private async handleResearchRequest(prompt: string): Promise<string> {
    try {
      console.log(`[StrategicDirectorAgent] Handling research request: "${prompt.substring(0, 100)}${prompt.length > 100 ? '...' : ''}"`);
      const researchTool = createResearchInsightsTool(this.userId);
      console.log(`[StrategicDirectorAgent] Created ResearchInsightsTool instance`);

      const operationPrompt = `
      You are analyzing a user's research request to determine the most appropriate research operation to perform.

      Available operations:
      1. researchMarketingTopic - For general marketing topic research
      2. conductMarketingResearch - For comprehensive marketing research on a specific topic
      3. conductCompetitiveAnalysis - For analyzing competitors in a specific industry
      4. conductMarketResearch - For market research on a specific product

      User Request: "${prompt}"

      Analyze the request and extract the following information in JSON format:
      {
        "operation": "one of the operations listed above",
        "topic": "the main topic or subject of the research (for researchMarketingTopic and conductMarketingResearch)",
        "researchQuestions": ["specific questions to address in the research (optional)"],
        "useInternetSearch": boolean (whether internet search should be used),
        "context": "additional context for the research (optional)",
        "productName": "the name of the product (for conductCompetitiveAnalysis or conductMarketResearch)",
        "industry": "the industry (for conductCompetitiveAnalysis)",
        "targetAudience": "the target audience (for conductMarketResearch)",
        "uniqueSellingPoints": "the unique selling points (for conductMarketResearch)"
      }

      Only include the fields that are relevant to the selected operation.
      `;
      const modelOptions = { temperature: 0.3 };
      const operationResponse = await this.llmTool.processContent({
        prompt: operationPrompt,
        model: this.defaultLlmModel,
        provider: this.defaultLlmProvider,
        modelOptions
      });
      const extractedJson = extractJsonFromText(operationResponse);
      const operationData = JSON.parse(extractedJson);

      console.log(`[StrategicDirectorAgent] Selected operation: ${operationData.operation}`);
      console.log(`[StrategicDirectorAgent] Operation parameters:`, operationData);
      console.log(`[StrategicDirectorAgent] Delegating to ResearchInsightsAgent...`);

      let researchResult;
      switch (operationData.operation) {
        case 'researchMarketingTopic':
          console.log(`[StrategicDirectorAgent] Calling researchMarketingTopic with topic: "${operationData.topic}"`);
          researchResult = await researchTool.researchMarketingTopic({
            topic: operationData.topic,
            researchQuestions: operationData.researchQuestions,
            useInternetSearch: operationData.useInternetSearch
          });
          break;
        case 'conductMarketingResearch':
          console.log(`[StrategicDirectorAgent] Calling conductMarketingResearch with topic: "${operationData.topic}"`);
          researchResult = await researchTool.conductMarketingResearch({
            topic: operationData.topic,
            context: operationData.context,
            researchQuestions: operationData.researchQuestions
          });
          break;
        case 'conductCompetitiveAnalysis':
          console.log(`[StrategicDirectorAgent] Calling conductCompetitiveAnalysis for product: "${operationData.productName}"`);
          researchResult = await researchTool.conductCompetitiveAnalysis({
            productName: operationData.productName,
            industry: operationData.industry
          });
          break;
        case 'conductMarketResearch':
          console.log(`[StrategicDirectorAgent] Calling conductMarketResearch for product: "${operationData.productName}"`);
          researchResult = await researchTool.conductMarketResearch({
            productName: operationData.productName,
            targetAudience: operationData.targetAudience,
            uniqueSellingPoints: operationData.uniqueSellingPoints
          });
          break;
        default:
          throw new Error(`Unknown research operation: ${operationData.operation}`);
      }

      console.log(`[StrategicDirectorAgent] Research operation completed successfully`);
      console.log(`[StrategicDirectorAgent] Formatting research results for user...`);

      let formattingPrompt: string;
      if (researchResult && researchResult.success === false) {
        console.log(`[StrategicDirectorAgent] Research operation returned an error: ${researchResult.error}`);
        formattingPrompt = `
        You are the Strategic Director Agent. You've delegated a research request to the Research & Insights Agent, but encountered an error:
        ${JSON.stringify(researchResult)}
        Create a helpful error response for the user that:
        1. Clearly indicates this was a Research & Insights Agent error
        2. Explains the nature of the error in user-friendly terms
        3. Provides strategic reasoning about why the requested research would have been valuable
        4. Suggests alternative approaches or more specific parameters the user could try
        5. Offers next steps to help the user get the information they need
        Format your response in a professional, easy-to-read manner using markdown formatting.
        IMPORTANT: Include a section at the beginning that clearly indicates this response was generated by the Strategic Director Agent based on a delegation to the Research & Insights Agent.
        `;
      } else {
        console.log(`[StrategicDirectorAgent] Formatting successful research results...`);
        formattingPrompt = `
        You are the Strategic Director Agent. You've delegated a research request to the Research & Insights Agent and received the following result:
        ${JSON.stringify(researchResult)}
        Format this information into a clear, well-structured response for the user. Include:
        1. Strategic Reasoning: Explain why this research is valuable from a strategic perspective
        2. Research results: Present the FULL research insight result from the Research InsightAgent - DO NOT summarize or truncate this content
        3. Strategic Implications: Explain how these findings can inform marketing strategy
        Present your response in a professional, easy-to-read markdown format.
        IMPORTANT: Include a section at the beginning that clearly indicates this response was generated by the Research & Insights Agent, delegated by the Strategic Director Agent.
        CRITICAL: Do NOT summarize the research results. Present the COMPLETE and DETAILED research report exactly as provided by the Research & Insights Agent. The full detailed report is the most valuable part of this response and must be included in its entirety.
        `;
      }

      const formattedResponse = await this.llmTool.processContent({
        prompt: formattingPrompt,
        model: this.defaultLlmModel,
        provider: this.defaultLlmProvider,
        modelOptions: { temperature: 0.5 }
      });

      this.storeInteraction(prompt, formattedResponse);
      console.log(`[StrategicDirectorAgent] Research request handling completed successfully`);
      return formattedResponse;
    } catch (error) {
      console.error('[StrategicDirectorAgent] Error handling research request:', error);
      return `I encountered an error while processing your research request: ${error instanceof Error ? error.message : 'Unknown error'}. Please try again with more specific details.`;
    }
  }
  /**
   * Analyze a task and recommend appropriate teams
   * This method is required by the IStrategicDirectorAgent interface used in PMOAssessmentAgent
   */
  async analyzeTask(params: {
    title: string;
    description: string;
    context: string;
    taskType: string;
  }): Promise<{
    success: boolean;
    recommendedTeams?: AgenticTeamId[];
    rationale?: string;
    error?: string;
  }> {
    console.log(`StrategicDirectorAgent: Analyzing task "${params.title}"`);

    try {
      const prompt = `
      You are a Strategic Director responsible for analyzing tasks and determining which specialized teams should handle them.

      TASK INFORMATION:
      Title: ${params.title}
      Description: ${params.description}
      Context: ${params.context}
      Task Type: ${params.taskType}

      AVAILABLE TEAMS:
      1. Marketing Team - Specializes in marketing strategy, campaigns, branding, and market analysis
      2. Research Team - Specializes in market research, data analysis, and insights generation
      3. Software Design Team - Specializes in software development, UI/UX design, and technical implementation
      4. Sales Team - Specializes in sales strategy, customer acquisition, and revenue generation
      5. Business Analysis Team - Specializes in business process analysis, requirements gathering, and solution design

      INSTRUCTIONS:
      1. Analyze the task details carefully
      2. Determine which team(s) would be best suited to handle this task
      3. Provide a clear rationale for your team selection
      4. Return your analysis in the required format

      Your response should be thoughtful and strategic, considering the specific needs of the task and the specialized capabilities of each team.
      `;

      const modelOptions = { temperature: 0.3 };
      const analysisResponse = await this.llmTool.processContent({
        prompt,
        model: this.defaultLlmModel,
        provider: this.defaultLlmProvider,
        modelOptions
      });

      // Extract team recommendations and rationale from the response
      const teamMapping: Record<string, AgenticTeamId> = {
        'marketing': AgenticTeamId.Marketing,
        'research': AgenticTeamId.Research,
        'software design': AgenticTeamId.SoftwareDesign,
        'sales': AgenticTeamId.Sales,
        'business analysis': AgenticTeamId.BusinessAnalysis
      };

      // Parse the response to extract team recommendations
      const recommendedTeams: AgenticTeamId[] = [];
      let rationale = '';

      // Simple parsing logic - can be enhanced with more sophisticated extraction
      const lines = analysisResponse.split('\n');
      let inRationale = false;

      for (const line of lines) {
        const lowerLine = line.toLowerCase();

        // Check for team mentions
        for (const [teamName, teamId] of Object.entries(teamMapping)) {
          if (lowerLine.includes(teamName)) {
            if (!recommendedTeams.includes(teamId)) {
              recommendedTeams.push(teamId);
            }
          }
        }

        // Collect rationale
        if (lowerLine.includes('rationale') || inRationale) {
          inRationale = true;
          rationale += line + '\n';
        }
      }

      // If no rationale was explicitly marked, use the whole response
      if (!rationale) {
        rationale = analysisResponse;
      }

      return {
        success: true,
        recommendedTeams,
        rationale: rationale.trim()
      };
    } catch (error) {
      console.error(`Error analyzing task in StrategicDirectorAgent:`, error);
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  /**
   * Analyze marketing performance and provide optimization recommendations
   */
  async analyzeMarketingPerformance(performanceData: any, context: string = '', objectives: string[] = []): Promise<any> {
    const prompt = `
    Analyze the following marketing performance data and provide detailed insights and optimization recommendations.

    Performance Data: ${JSON.stringify(performanceData)}
    Context: ${context}
    Objectives: ${objectives.join(', ')}

    Please provide a detailed performance analysis with the following sections:
    1. Executive Summary - Overview of key findings
    2. Performance Metrics Analysis - Detailed analysis of each metric
    3. Channel Performance - Analysis of performance by channel
    4. Audience Insights - How different audience segments responded
    5. Content Performance - Which content performed best and why
    6. ROI Analysis - Return on investment for marketing activities
    7. Competitive Benchmarking - How performance compares to industry standards (if data available)
    8. Trend Analysis - Patterns and trends in the data
    9. Optimization Recommendations - Specific recommendations for improvement
    10. Implementation Plan - How to implement the recommendations (high-level)
    11. Future Metrics - Additional metrics to track going forward

    IMPORTANT: Format your response strictly as a valid JSON object with these sections as keys. Follow these guidelines:
    - Use double quotes for all keys and string values.
    - Ensure all arrays have proper brackets and commas.
    - Avoid trailing commas in arrays and objects.
    - Ensure all opening brackets/braces have matching closing ones.
    - For string arrays, use simple strings without nested objects.

    Begin your response with the JSON object and nothing else. Do not include any explanatory text before or after the JSON.
    `;
    const modelOptions = { temperature: 0.2 };
    const analysisJson = await this.llmTool.processContent({
        prompt,
        model: this.defaultLlmModel,
        provider: this.defaultLlmProvider,
        modelOptions
    });

    try {
      const extractedJson = extractJsonFromText(analysisJson);
      console.log('Extracted JSON from performance analysis:', extractedJson.substring(0, 100) + '...');
      const analysisData = JSON.parse(extractedJson);

      if (!this.memory.Agent_Response.performanceAnalyses) {
        this.memory.Agent_Response.performanceAnalyses = [];
      }
      const analysisEntry = {
        id: `analysis-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
        performanceData,
        context,
        objectives,
        analysis: analysisData,
        createdAt: new Date()
      };
      this.memory.Agent_Response.performanceAnalyses.push(analysisEntry);
      await this.saveMemoryToStorage();
      return analysisData;
    } catch (error) {
      console.error('Error processing performance analysis:', error);
      return {
        success: false,
        error: 'Failed to parse performance analysis JSON response',
        rawResponse: analysisJson
      };
    }
  }

  /**
   * Create a SWOT analysis chart - ABSTRACTED TO UTILITY
   */
  async createSwotAnalysisChart(productId: string): Promise<ChartGenerationResult> {
    const productAnalysis = this.productAnalyses.find(p => p.id === productId);
    if (!productAnalysis) throw new Error(`Product analysis not found for ID: ${productId}`);

    return StrategicChartGenerator.createSwotAnalysisChart(productAnalysis);
  }

  /**
   * Create a competitive analysis chart - ABSTRACTED TO UTILITY
   */
  async createCompetitiveAnalysisChart(productId: string): Promise<ChartGenerationResult> {
    const productAnalysis = this.productAnalyses.find(p => p.id === productId);
    if (!productAnalysis) throw new Error(`Product analysis not found for ID: ${productId}`);

    return StrategicChartGenerator.createCompetitiveAnalysisChart(productAnalysis);
  }

  /**
   * Create a target audience segmentation chart - ABSTRACTED TO UTILITY
   */
  async createTargetAudienceChart(strategyId: string): Promise<ChartGenerationResult> {
    const strategy = this.strategies.find(s => s.id === strategyId);
    if (!strategy) throw new Error(`Strategy not found for ID: ${strategyId}`);

    return StrategicChartGenerator.createTargetAudienceChart(strategy);
  }

  /**
   * Create a marketing channels effectiveness chart - ABSTRACTED TO UTILITY
   */
  async createChannelEffectivenessChart(strategyId: string): Promise<ChartGenerationResult> {
    const strategy = this.strategies.find(s => s.id === strategyId);
    if (!strategy) throw new Error(`Strategy not found for ID: ${strategyId}`);

    return StrategicChartGenerator.createChannelEffectivenessChart(strategy);
  }

  /**
   * Create a marketing timeline visualization - ABSTRACTED TO UTILITY
   */
  async createMarketingTimelineChart(strategyId: string): Promise<ChartGenerationResult> {
    const strategy = this.strategies.find(s => s.id === strategyId);
    if (!strategy || !strategy.timeline || !strategy.timeline.milestones) throw new Error(`Strategy/timeline not found for ID: ${strategyId}`);

    return StrategicChartGenerator.createMarketingTimelineChart(strategy);
  }

  /**
   * Create a KPI dashboard visualization - ABSTRACTED TO UTILITY
   */
  async createKpiDashboardChart(strategyId: string): Promise<ChartGenerationResult> {
    const strategy = this.strategies.find(s => s.id === strategyId);
    if (!strategy || !strategy.kpis) throw new Error(`Strategy/KPIs not found for ID: ${strategyId}`);

    return StrategicChartGenerator.createKpiDashboardChart(strategy);
  }

  /**
   * Generate a strategy document as PDF
   */
  async generateStrategyDocument(strategyId: string): Promise<Buffer | SavePdfToByteStoreResult> {
    const strategy = this.strategies.find(s => s.id === strategyId);
    if (!strategy) throw new Error(`Strategy not found for ID: ${strategyId}`);

    const productAnalysis = this.productAnalyses.find(p =>
      strategy.name.includes(p.name || "") || (p.name && strategy.name && p.name.includes(strategy.name || ""))
    );

    let swotChart: ChartConfigType | null = null, audienceChart: ChartConfigType | null = null,
        channelChart: ChartConfigType | null = null, timelineChart: ChartConfigType | null = null,
        kpiChart: ChartConfigType | null = null;
    this.chartFallbacks = {};

    try {
      const chartPromises = [];
      if (productAnalysis) {
        chartPromises.push(this.createSwotAnalysisChart(productAnalysis.id)
          .then(r => { swotChart = r.chartConfig; if (r.fallbackDescription && !r.chartConfig) this.chartFallbacks.swot = r.fallbackDescription; })
          .catch(e => { console.error('SWOT chart error:', e); this.chartFallbacks.swot = `SWOT (Chart failed): Strengths: ...`; }));
      }
      chartPromises.push(this.createTargetAudienceChart(strategyId)
        .then(r => { audienceChart = r.chartConfig; if (r.fallbackDescription && !r.chartConfig) this.chartFallbacks.audience = r.fallbackDescription; })
        .catch(e => { console.error('Audience chart error:', e); this.chartFallbacks.audience = `Audience (Chart failed): Demographics: ...`; }));
      chartPromises.push(this.createChannelEffectivenessChart(strategyId)
        .then(r => { channelChart = r.chartConfig; if (r.fallbackDescription && !r.chartConfig) this.chartFallbacks.channels = r.fallbackDescription; })
        .catch(e => { console.error('Channel chart error:', e); this.chartFallbacks.channels = `Channels (Chart failed): List: ...`; }));
      chartPromises.push(this.createMarketingTimelineChart(strategyId)
        .then(r => { timelineChart = r.chartConfig; if (r.fallbackDescription && !r.chartConfig) this.chartFallbacks.timeline = r.fallbackDescription; })
        .catch(e => { console.error('Timeline chart error:', e); this.chartFallbacks.timeline = `Timeline (Chart failed): Milestones: ...`; }));
      chartPromises.push(this.createKpiDashboardChart(strategyId)
        .then(r => { kpiChart = r.chartConfig; if (r.fallbackDescription && !r.chartConfig) this.chartFallbacks.kpis = r.fallbackDescription; })
        .catch(e => { console.error('KPI chart error:', e); this.chartFallbacks.kpis = `KPIs (Chart failed): List: ...`; }));
      await Promise.all(chartPromises);
    } catch (error) {
      console.error('Chart generation phase error:', error);
      this.chartFallbacks.general = 'One or more charts failed.';
    }

    const pdfContents: PdfContent[] = [
      { title: strategy.name, content: strategy.description },
      { title: 'Marketing Objectives', content: (strategy.objectives || []).map(obj => `• ${obj}`).join('\n\n') }
    ];
    if (this.chartFallbacks.general) pdfContents.push({ title: 'Visualization Note', content: this.chartFallbacks.general });
    if (strategy.executiveSummary) pdfContents.push({ title: 'Executive Summary', content: strategy.executiveSummary });

    pdfContents.push({
      title: 'Target Audience',
      content: `**Demographics:**\n${(strategy.targetAudience.demographics || []).map(d => `• ${d}`).join('\n')}\n\n**Psychographics:**\n${(strategy.targetAudience.psychographics || []).map(p => `• ${p}`).join('\n')}\n\n**Behaviors:**\n${(strategy.targetAudience.behaviors || []).map(b => `• ${b}`).join('\n')}${strategy.targetAudience.description ? `\n\n**Audience Description:**\n${strategy.targetAudience.description}` : ''}`
    });
    if (audienceChart) pdfContents.push({ title: 'Audience Segmentation Visualization', content: `<chart-data>${JSON.stringify(audienceChart)}</chart-data>` });
    else if (this.chartFallbacks.audience) pdfContents.push({ title: 'Audience Segmentation (Text)', content: this.chartFallbacks.audience });

    pdfContents.push(
      { title: 'Value Proposition', content: strategy.valueProposition },
      { title: 'Key Messages', content: (strategy.keyMessages || []).map(msg => `• ${msg}`).join('\n\n') }
    );

    let channelContent = (strategy.channels || []).map(ch => `• ${ch}`).join('\n\n');
    if (strategy.channelStrategy && Object.keys(strategy.channelStrategy).length > 0) channelContent += `\n\n**Channel Strategy:**\n${Object.entries(strategy.channelStrategy).map(([c, s]) => `• **${c}:** ${s}`).join('\n')}`;
    if (strategy.contentStrategy) channelContent += `\n\n**Content Strategy:**\n${strategy.contentStrategy}`;
    pdfContents.push({ title: 'Marketing Channels & Content Strategy', content: channelContent });
    if (channelChart) pdfContents.push({ title: 'Channel Effectiveness Analysis', content: `<chart-data>${JSON.stringify(channelChart)}</chart-data>` });
    else if (this.chartFallbacks.channels) pdfContents.push({ title: 'Channel Effectiveness (Text)', content: this.chartFallbacks.channels });

    pdfContents.push({
      title: 'Timeline',
      content: `**Start:** ${strategy.timeline.startDate.toLocaleDateString()}, **End:** ${strategy.timeline.endDate.toLocaleDateString()}\n${strategy.timeline.description ? `**Desc:** ${strategy.timeline.description}\n` : ''}\n**Milestones:**\n${(strategy.timeline.milestones || []).map(m => `• **${m.name}** (${(m.date instanceof Date ? m.date : new Date(String(m.date))).toLocaleDateString()}): ${m.description}`).join('\n\n')}`
    });
    if (timelineChart) pdfContents.push({ title: 'Timeline Visualization', content: `<chart-data>${JSON.stringify(timelineChart)}</chart-data>` });
    else if (this.chartFallbacks.timeline) pdfContents.push({ title: 'Timeline (Text)', content: this.chartFallbacks.timeline });

    pdfContents.push({ title: 'Key Performance Indicators', content: (strategy.kpis || []).map(kpi => `• ${kpi}`).join('\n\n') });
    if (kpiChart) pdfContents.push({ title: 'KPI Dashboard', content: `<chart-data>${JSON.stringify(kpiChart)}</chart-data>` });
    else if (this.chartFallbacks.kpis) pdfContents.push({ title: 'KPI Dashboard (Text)', content: this.chartFallbacks.kpis });

    if (strategy.budget && (strategy.budget.total || strategy.budget.breakdown || strategy.budget.notes)) {
        let budgetStr = '';
        if (strategy.budget.total) budgetStr += `**Total:** $${strategy.budget.total.toLocaleString()}\n`;
        if (strategy.budget.breakdown) budgetStr += `**Breakdown:**\n${Object.entries(strategy.budget.breakdown).map(([cat, val]) => `• ${cat}: $${(val as number).toLocaleString()}`).join('\n')}\n`;
        if (strategy.budget.notes) budgetStr += `**Notes:** ${strategy.budget.notes}`;
        pdfContents.push({ title: 'Budget', content: budgetStr });
    }
    if (strategy.riskAssessment && strategy.riskAssessment.length > 0) {
        pdfContents.push({ title: 'Risk Assessment', content: (strategy.riskAssessment || []).map(r => `**Risk:** ${r.risk}\n**Impact:** ${r.impact}\n**Mitigation:** ${r.mitigation}`).join('\n\n') });
    }

    if (productAnalysis) {
        let productContent = `**USPs:**\n${(productAnalysis.uniqueSellingPoints || []).map(u => `• ${u}`).join('\n')}\n\n` +
                             `**Advantages:**\n${(productAnalysis.competitiveAdvantages || []).map(c => `• ${c}`).join('\n')}\n\n` +
                             `**SWOT:** Strengths: ${(productAnalysis.swotAnalysis.strengths || []).join(', ')}. Weaknesses: ... Opportunities: ... Threats: ...`;
        pdfContents.push({ title: `Product Analysis: ${productAnalysis.name}`, content: productContent });
        if (swotChart) pdfContents.push({ title: 'SWOT Visualization', content: `<chart-data>${JSON.stringify(swotChart)}</chart-data>` });
        else if (this.chartFallbacks.swot) pdfContents.push({ title: 'SWOT (Text)', content: this.chartFallbacks.swot });
    }

    const pdfOptions: PdfGenerationOptions = {
      title: strategy.name, subtitle: 'Marketing Strategy Document',
      date: dateTimeTool.getCurrentDate({ format: 'full' }), saveToByteStore: true,
      agentId: this.id, agentName: this.name, category: 'Marketing Agent Team',
      strategyId: strategy.id, documentType: 'marketing_strategy'
    };
    return await this.pdfGeneratorTool.generatePdf(pdfContents, pdfOptions);
  }

  /**
   * Coordinate tasks between agents
   */
  async coordinateAgentTasks(
    researchAgentId: string, contentCreatorAgentId: string, socialMediaAgentId: string, analyticsAgentId: string, strategyId: string
  ): Promise<void> {
    const strategy = this.strategies.find(s => s.id === strategyId);
    if (!strategy) throw new Error(`Strategy not found for ID: ${strategyId}`);

    const researchTasks = [
      { title: 'Competitive Analysis', description: `Conduct competitive analysis for ${strategy.name}. Areas: ${strategy.competitiveAnalysis || 'market'}`, priority: 'high' as const },
      { title: 'Audience Deeper Dive', description: `Research audience for ${strategy.name}. Focus: ${JSON.stringify(strategy.targetAudience)}`, priority: 'high' as const }
    ];
    const contentTasks = [
      { title: 'Content Pillar ID', description: `Develop content pillars for ${strategy.name}, VP: "${strategy.valueProposition}", Msgs: "${(strategy.keyMessages || []).join('; ')}"`, priority: 'high' as const }
    ];
    const socialMediaTasks = [
      { title: 'Social Content Calendar', description: `Create social calendar for ${strategy.name}. Channels: ${(strategy.channels || []).join(', ')}`, priority: 'medium' as const }
    ];
    const analyticsTasks = [
      { title: 'KPI Tracking Setup', description: `Set up tracking for KPIs: ${(strategy.kpis || []).join(', ')}`, priority: 'high' as const }
    ];

    for (const task of researchTasks) this.createTask(task.title, task.description, task.priority, researchAgentId);
    for (const task of contentTasks) this.createTask(task.title, task.description, task.priority, contentCreatorAgentId);
    for (const task of socialMediaTasks) this.createTask(task.title, task.description, task.priority, socialMediaAgentId);
    for (const task of analyticsTasks) this.createTask(task.title, task.description, task.priority, analyticsAgentId);

    await this.sendMessage(researchAgentId, `New strategy "${strategy.name}" (ID: ${strategy.id}). Review assigned research tasks.`);
    await this.sendMessage(contentCreatorAgentId, `Strategy "${strategy.name}" (ID: ${strategy.id}) ready. Awaiting research insights from ${researchAgentId}.`);
    await this.sendMessage(socialMediaAgentId, `Strategy "${strategy.name}" (ID: ${strategy.id}) established. Prepare social strategies.`);
    await this.sendMessage(analyticsAgentId, `Strategy "${strategy.name}" (ID: ${strategy.id}) active. Set up KPI tracking.`);
  }

  getStrategies(): MarketingStrategy[] { return this.strategies; }
  getStrategy(strategyId: string): MarketingStrategy | undefined { return this.strategies.find(s => s.id === strategyId); }
  getProductAnalyses(): ProductAnalysis[] { return this.productAnalyses; }
  getProductAnalysis(productId: string): ProductAnalysis | undefined { return this.productAnalyses.find(p => p.id === productId); }

  getLatestDocumentUrl(): string | null {
    if (this.memory.Agent_Response.documentUrls && this.memory.Agent_Response.documentUrls.length > 0) {
      const sortedDocs = [...this.memory.Agent_Response.documentUrls].sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
      return sortedDocs[0]?.url || null;
    }
    return null;
  }

  async processMessages(): Promise<void> {
    const messages = await this.receiveMessages();
    for (const message of messages) {
      console.log(`StrategicDirectorAgent: Processing message ID ${message.metadata?.messageId || 'N/A'} from ${message.from}: ${message.content.substring(0, 100)}...`);
      if (message.metadata?.messageType === 'information_request') {
        await this.handleInformationRequest(message);
      } else if (message.metadata?.requiresResponse) {
        const responseText = await this.processRequest(message.content);
        await this.sendMessage(message.from, responseText, {
          inResponseTo: message.metadata?.messageId, isResponse: true, messageType: 'standard_response'
        });
      } else {
        console.log(`StrategicDirectorAgent: Received informational message from ${message.from}. No direct response required.`);
        this.storeInteraction(`Informational message from ${message.from}`, message.content);
      }
    }
  }

  private async handleInformationRequest(message: AgentMessage): Promise<void> {
    const { from, content, metadata } = message;
    const requestType = metadata?.requestType || 'clarification';
    const taskId = metadata?.taskId;
    console.log(`StrategicDirectorAgent: Handling ${requestType} request (ID: ${metadata?.messageId || 'N/A'}) from ${from}`);

    const questionsMatch = content.match(/### Questions:\n([\s\S]*?)(?=###|$)/i);
    const questionsText = questionsMatch ? questionsMatch[1].trim() : content;
    const topicMatch = content.match(/\*\*Topic:\*\* (.*)/i);
    const topic = topicMatch ? topicMatch[1] : 'an unspecified topic';

    const prompt = `
You are the Strategic Director. You received an information request from ${from} agent.
Topic: ${topic}. Task ID: ${taskId || 'N/A'}.
Agent's request/questions: "${questionsText}"
Provide a clear, concise, helpful response. Focus on specific information/guidance.
Be strategic, align with marketing objectives. Format as plain text for inter-agent communication.`;

    const modelOptions = { temperature: 0.5 };
    const responseText = await this.llmTool.processContent({
        prompt, model: this.defaultLlmModel, provider: this.defaultLlmProvider, modelOptions
    });
    await this.sendMessage(from, responseText, {
      messageType: 'information_response', requestType, inResponseTo: metadata?.messageId, taskId, isResponse: true
    });
    console.log(`StrategicDirectorAgent: Sent response to ${from}'s info request about ${topic}`);
    this.storeInteraction(`Info request from ${from} about ${topic}`, `Response: ${responseText.substring(0,100)}...`);
  }

  async generateNarrativeReport(strategyId: string, reportType: string = 'strategy_overview', audience: string = 'executive'): Promise<string> {
    const strategy = this.strategies.find(s => s.id === strategyId);
    if (!strategy) throw new Error(`Strategy not found for ID: ${strategyId}`);

    const productAnalysis = this.productAnalyses.find(p =>
      strategy.name.includes(p.name || "") || (p.name && strategy.name && p.name.includes(strategy.name || ""))
    );
    const reportDateTime = dateTimeTool.getCurrentDateTime({ format: 'full', includeTime: true, includeDayOfWeek: true });

    let prompt = `
    Generate a narrative marketing report.
    Type: "${reportType}", Audience: "${audience}", Date: ${reportDateTime}
    Strategy: ${strategy.name} (${strategy.description || 'N/A'})
    Objectives: ${(strategy.objectives || []).map(o => `- ${o}`).join('\n')}
    Target Audience: Demographics: ${(strategy.targetAudience.demographics || []).join(', ') || 'N/A'}, Psychographics: ..., Behaviors: ...
    Value Proposition: ${strategy.valueProposition || 'N/A'}
    Key Messages: ${(strategy.keyMessages || []).map(m => `- ${m}`).join('\n') || 'N/A'}
    Channels: ${(strategy.channels || []).join(', ') || 'N/A'}
    Timeline: Start: ${strategy.timeline.startDate.toLocaleDateString()}, End: ${strategy.timeline.endDate.toLocaleDateString()}, Milestones: ...
    KPIs: ${(strategy.kpis || []).map(k => `- ${k}`).join('\n') || 'N/A'}
    `;
    if (productAnalysis) {
      prompt += `\nProduct Analysis (${productAnalysis.name}): USPs: ${(productAnalysis.uniqueSellingPoints || []).join(', ') || 'N/A'}, SWOT: ...`;
    }
    prompt += `\nINSTRUCTIONS: Create compelling narrative. Well-structured. Adapt tone for audience. Focus on insights. DO NOT use JSON.`;

    const modelOptions = { temperature: 0.7 };
    const reportContent = await this.llmTool.processContent({
        prompt, model: this.defaultLlmModel, provider: this.defaultLlmProvider, modelOptions
    });

    if (!this.memory.Agent_Response.reports) this.memory.Agent_Response.reports = [];
    const report = {
      id: `report-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
      strategyId, reportType, audience, content: reportContent, createdAt: new Date(),
      formattedTimestamp: dateTimeTool.getCurrentDateTime({ format: 'full', includeTime: true, use24HourFormat: false })
    };
    this.memory.Agent_Response.reports.push(report);
    await this.saveMemoryToStorage();
    return reportContent;
  }

  /**
   * Create a strategic implementation plan based on PMO requirements
   * This method follows the same workflow as marketing-agent-tests Strategic Analysis
   */
  async createPMOStrategicPlan(params: {
    pmoId: string;
    projectTitle: string;
    projectDescription: string;
    pmoAssessment: string;
    teamSelectionRationale: string;
    priority: string;
    category: string;
    requirementsDocument?: string;
  }): Promise<{
    success: boolean;
    strategicPlan?: string;
    documentTitle?: string;
    documentUrl?: string;
    error?: string;
  }> {
    try {
      console.log(`StrategicDirectorAgent: Creating strategic implementation plan for PMO project: ${params.projectTitle}`);

      const currentDate = new Date().toISOString();
      const documentTitle = `Strategic Implementation Plan - ${params.projectTitle} (Marketing) - (${params.pmoId})`;

      // Step 1: Query documents for relevant context (same as marketing-agent-tests)
      console.log(`StrategicDirectorAgent: Querying documents for PMO category: ${params.category}`);

      const documentQuery = `Find information related to: ${params.projectTitle} ${params.projectDescription} PMO requirements and strategic planning context`;

      let documentContext = '';
      try {
        const queryResult = await this.queryDocumentsEnhanced(
          documentQuery,
          params.category, // Use the PMO category
          undefined, // No specific filename
          undefined, // No specific namespace
          true, // Use internet search as fallback
          'gemini-2.5-pro-preview-05-06' // Use high-performance model
        );

        if (queryResult.success) {
          documentContext = queryResult.content;
          console.log(`StrategicDirectorAgent: Successfully retrieved document context (${documentContext.length} characters)`);
        } else {
          console.log(`StrategicDirectorAgent: No relevant documents found, proceeding with PMO requirements only`);
        }
      } catch (queryError) {
        console.warn(`StrategicDirectorAgent: Document query failed, proceeding without additional context:`, queryError);
      }

      // Step 2: Create strategic plan prompt with document context
      const strategicPlanPrompt = `
You are the Strategic Director of the Marketing Team. You have received a project assignment from the PMO (Project Management Office) and need to create a comprehensive Strategic Implementation Plan.

PMO PROJECT DETAILS:
- Project Title: ${params.projectTitle}
- Project ID: ${params.pmoId}
- Priority: ${params.priority}
- Project Description: ${params.projectDescription}

PMO ASSESSMENT:
${params.pmoAssessment}

TEAM SELECTION RATIONALE:
${params.teamSelectionRationale}

REQUIREMENTS DOCUMENT CONTENT:
${params.requirementsDocument || 'No additional requirements document provided'}

${documentContext ? `
RELEVANT DOCUMENT CONTEXT:
The following information was found in related documents and should inform your strategic planning:

${documentContext}

Based on this context and the PMO requirements above, ` : 'Based on the PMO requirements above, '}

INSTRUCTIONS:
Create a detailed Strategic Implementation Plan that leverages the Marketing team's specialized expertise. This plan should be significantly more detailed and strategic than a generic PMO plan.

Your plan must include:

1. **Executive Summary**
   - Project overview from marketing perspective
   - Strategic approach summary
   - Expected outcomes and impact

2. **Marketing Strategy Analysis**
   - Market opportunity assessment
   - Target audience identification
   - Competitive landscape analysis
   - Value proposition development

3. **Implementation Framework**
   - Phase-by-phase implementation approach
   - Marketing channels and tactics
   - Content strategy and messaging
   - Brand positioning considerations

4. **Resource Planning**
   - Team structure and roles
   - Budget allocation by marketing function
   - Technology and tools requirements
   - External vendor/agency needs

5. **Timeline and Milestones**
   - Detailed project timeline with marketing-specific milestones
   - Campaign launch dates
   - Review and optimization points
   - Deliverable schedule

6. **Success Metrics and KPIs**
   - Marketing-specific KPIs
   - ROI measurement framework
   - Performance tracking methodology
   - Reporting schedule

7. **Risk Assessment and Mitigation**
   - Marketing-specific risks
   - Contingency plans
   - Quality assurance measures

8. **Stakeholder Management**
   - Internal stakeholder communication plan
   - External partner coordination
   - Approval workflows

IMPORTANT: In your response, you MUST include a section titled "Strategic Reasoning" that explains your analysis and thought process behind the strategy you're proposing. This section should document:

1. Your analysis of the marketing situation
2. The strategic frameworks you applied
3. How your recommendations align with business objectives
4. How different team members (Research, Content, Social Media, Analytics) would contribute to this strategy
5. Key metrics for measuring success

Format the response as a comprehensive markdown document that demonstrates the Marketing team's strategic thinking and domain expertise. This should be a professional document that could be presented to executives.

Date: ${currentDate}
`;

      // Step 3: Generate strategic plan using processRequest (same as marketing-agent-tests)
      console.log(`StrategicDirectorAgent: Processing strategic plan request with document context`);

      const strategicPlan = await this.processRequest(strategicPlanPrompt, documentContext, params.category);

      // Step 4: Get the latest document URL (PDF generation happens automatically in processRequest)
      const latestDocUrl = this.getLatestDocumentUrl();

      // Step 5: Save the strategic plan using the team strategic plan API
      const saveResponse = await fetch('/api/team-strategic-plan', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          pmoId: params.pmoId,
          teamId: 'Marketing',
          teamName: 'Marketing',
          projectTitle: params.projectTitle,
          strategicPlanContent: strategicPlan,
          userId: this.userId,
          category: params.category,
          agentType: 'Marketing_Strategic_Director',
          metadata: {
            createdBy: 'Strategic Director Agent',
            projectDescription: params.projectDescription,
            pmoAssessment: params.pmoAssessment,
            priority: params.priority,
            planType: 'Strategic Implementation Plan',
            version: '1.0',
            documentContext: documentContext ? 'Used document context' : 'No document context available',
            documentUrl: latestDocUrl
          }
        }),
      });

      const saveResult = await saveResponse.json();

      if (!saveResponse.ok || !saveResult.success) {
        throw new Error(saveResult.error || 'Failed to save strategic plan');
      }

      // Step 6: Store in agent memory
      if (!this.memory.Agent_Response.strategicPlans) {
        this.memory.Agent_Response.strategicPlans = [];
      }

      this.memory.Agent_Response.strategicPlans.push({
        id: saveResult.agentOutputId,
        pmoId: params.pmoId,
        projectTitle: params.projectTitle,
        documentTitle,
        content: strategicPlan,
        createdAt: new Date(),
        category: params.category,
        pdfUrl: saveResult.pdfUrl || latestDocUrl,
        documentContext: !!documentContext
      });

      await this.saveMemoryToStorage();

      console.log(`StrategicDirectorAgent: Successfully created strategic implementation plan for ${params.projectTitle}`);

      return {
        success: true,
        strategicPlan,
        documentTitle,
        documentUrl: saveResult.pdfUrl || latestDocUrl
      };

    } catch (error) {
      console.error('StrategicDirectorAgent: Error creating PMO strategic plan:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to create strategic plan'
      };
    }
  }

  /**
   * Generate strategic analysis charts for marketing insights - ABSTRACTED TO UTILITY
   */
  async generateStrategicCharts(
    analysisContent: string,
    projectTitle: string,
    analysisType: string = 'strategic'
  ): Promise<ChartGenerationResult[]> {
    return StrategicChartGenerator.generateStrategicCharts(analysisContent, projectTitle, analysisType);
  }

  /**
   * Generate charts as part of the strategic analysis process
   * This method integrates chart generation into the main workflow
   */
  async processRequestWithCharts(
    request: string,
    context?: string,
    category?: string
  ): Promise<{
    response: string;
    charts: ChartGenerationResult[];
    success: boolean;
    error?: string;
  }> {
    try {
      // Generate the main strategic response
      const response = await this.processRequest(request, context, category);

      // Generate supporting charts
      const charts = await this.generateStrategicCharts(response, 'Strategic Analysis');

      return {
        response,
        charts,
        success: true
      };
    } catch (error: any) {
      return {
        response: '',
        charts: [],
        success: false,
        error: error.message
      };
    }
  }
}