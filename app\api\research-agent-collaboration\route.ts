import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '../auth/[...nextauth]/authOptions';
import { ResearchTeamAgent } from '../../../lib/agents/research/ResearchTeamAgent';
import { ResearchAgentManager } from '../../../lib/agents/research/ResearchAgentManager';
import { Task } from '../../../admin/planner/types';
import { AgenticTeamId } from '../../../lib/agents/pmo/PMOInterfaces';

/**
 * Remove undefined values from an object recursively (Firestore doesn't allow undefined)
 */
function removeUndefinedValues(obj: any): any {
  if (obj === null || obj === undefined) {
    return null;
  }

  if (Array.isArray(obj)) {
    return obj.map(item => removeUndefinedValues(item)).filter(item => item !== undefined);
  }

  if (typeof obj === 'object') {
    const cleaned: any = {};
    for (const [key, value] of Object.entries(obj)) {
      if (value !== undefined) {
        cleaned[key] = removeUndefinedValues(value);
      }
    }
    return cleaned;
  }

  return obj;
}

// Extended Task interface for PMO operations that includes metadata
interface PMOTask extends Task {
  metadata?: {
    source?: string;
    pmoId?: string;
    pmoAssessment?: string;
    teamSelectionRationale?: string;
    requirementsDocument?: string;
    autoTriggered?: boolean;
    triggerTimestamp?: string;
    context?: string;
    [key: string]: any;
  };
}

/**
 * Research Agent Collaboration API
 *
 * This endpoint provides a standardized interface for PMO-to-Research team communication.
 * It processes PMO requests and initiates Research team workflows automatically.
 */
export async function POST(request: NextRequest) {
  try {
    // Get user session
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      );
    }

    const userId = session.user.email;
    const requestBody = await request.json();

    console.log('[Research API] Processing research collaboration request:', {
      userId,
      hasPrompt: !!requestBody.prompt,
      hasPMOContext: !!requestBody.metadata?.pmoId
    });

    // Validate required fields
    const { prompt, modelProvider, modelName, context, category, metadata } = requestBody;

    if (!prompt) {
      return NextResponse.json(
        { success: false, error: 'Prompt is required' },
        { status: 400 }
      );
    }

    // Determine if this is a PMO task or direct research request
    const isPMOTask = metadata?.source === 'PMO' && metadata?.pmoId;

    let result;

    if (isPMOTask) {
      // Process as PMO task using ResearchTeamAgent
      result = await processPMOTask(requestBody, userId);
    } else {
      // Process as direct research request using ResearchAgentManager
      result = await processDirectResearchRequest(requestBody, userId);
    }

    console.log('[Research API] Research collaboration completed:', {
      success: result.success,
      hasOutput: !!result.output,
      documentCount: result.outputDocumentIds?.length || 0
    });

    return NextResponse.json(result);

  } catch (error: any) {
    console.error('[Research API] Error in research collaboration:', error);
    return NextResponse.json(
      {
        success: false,
        error: error.message || 'Research collaboration failed',
        details: process.env.NODE_ENV === 'development' ? error.stack : undefined
      },
      { status: 500 }
    );
  }
}

/**
 * Process PMO task using standardized ResearchTeamAgent
 */
async function processPMOTask(requestBody: any, userId: string) {
  const { prompt, context, metadata, category } = requestBody;

  console.log('[Research API] Processing PMO task:', metadata.pmoId);

  // Create PMO Task object from request
  const pmoTask: PMOTask = {
    id: metadata.taskId || `pmo-research-${metadata.pmoId}-${Date.now()}`,
    projectId: 'pmo-project', // Add required projectId field
    title: metadata.projectTitle || metadata.recordTitle || 'PMO Research Task',
    description: prompt,
    category: category || 'Research',
    priority: metadata.priority?.toLowerCase() || 'medium',
    status: 'In Progress',
    startDate: new Date(), // Add required startDate field
    dueDate: metadata.deadline ? new Date(metadata.deadline) : new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
    assignedTo: [AgenticTeamId.Research],
    dependencies: [], // Add required dependencies field
    createdAt: new Date(),
    updatedAt: new Date(),
    metadata: {
      source: 'PMO',
      pmoId: metadata.pmoId,
      pmoAssessment: metadata.pmoAssessment,
      teamSelectionRationale: metadata.teamSelectionRationale,
      requirementsDocument: metadata.requirementsDocument,
      autoTriggered: metadata.autoTriggered || false,
      triggerTimestamp: metadata.triggerTimestamp,
      context: context
    }
  };

  // Initialize ResearchTeamAgent
  const researchTeamAgent = new ResearchTeamAgent({
    userId,
    includeExplanation: true,
    streamResponse: false // Can be enabled for real-time updates
  });

  // Process the PMO task
  const result = await researchTeamAgent.processTask(pmoTask);

  // Store the research output in global Agent_Output collection for PMO integration
  if (result.success && result.output) {
    try {
      const { v4: uuidv4 } = await import('uuid');
      const { adminDb } = await import('../../../components/firebase-admin');

      const requestId = uuidv4();

      // Extract PMO metadata for proper PMO Output Tab integration (matching Marketing team pattern)
      const recordTitle = metadata.projectTitle || metadata.recordTitle || 'Research Analysis';
      const projectTitle = metadata.projectTitle || recordTitle;

      // Prepare data to be stored in global Firestore collection (matching marketing workflow)
      const agentOutputData = {
        requestId,
        timestamp: new Date(),
        agentType: 'Research', // Research team agent type for PMO filtering (matches PMO Output Tab expectations)
        userId, // Add userId field for proper filtering in PMO Output tab
        prompt: requestBody.prompt,
        result: {
          thinking: '', // Research team doesn't expose thinking process
          output: result.output,
          documentUrl: (result as any).researchSpecificOutput?.strategicPlanUrl || null
        },
        agentMessages: [], // Research team uses internal coordination
        modelInfo: {
          provider: requestBody.modelProvider || 'anthropic',
          model: requestBody.modelName || 'claude-sonnet-4-20250514'
        },
        // PMO-specific metadata for proper integration (matching Marketing team pattern)
        ...(metadata.source === 'PMO' && {
          pmoMetadata: {
            source: metadata.source,
            pmoId: metadata.pmoId,
            recordTitle: recordTitle,
            projectTitle: projectTitle,
            teamId: AgenticTeamId.Research,
            teamName: 'Research',
            autoTriggered: metadata.autoTriggered || true,
            triggerTimestamp: metadata.triggerTimestamp,
            processedAt: new Date().toISOString()
          }
        }),
        // Include metadata fields at top level for easier access (matching Marketing team pattern)
        ...(recordTitle && {
          metadata: {
            recordTitle: recordTitle,
            projectTitle: projectTitle,
            source: metadata.source || 'PMO',
            pmoId: metadata.pmoId,
            teamName: 'Research',
            teamId: AgenticTeamId.Research
          }
        }),
        // Include category for filtering (matching Marketing team pattern)
        category: requestBody.category || `PMO - ${recordTitle} - ${metadata.pmoId}`,
        // Include context information
        contextOptions: {
          customContext: requestBody.context || null,
          documentReferences: requestBody.documentReferences || null,
          category: requestBody.category || null,
          pmoContext: metadata
        }
      };

      // Clean the data to remove undefined values (Firestore doesn't allow undefined)
      const cleanedData = removeUndefinedValues(agentOutputData);

      console.log(`[RESEARCH_AGENT_OUTPUT] Storing research analysis output with requestId: ${requestId}`);
      await adminDb.collection('Agent_Output').doc(requestId).set(cleanedData);
      console.log(`[RESEARCH_AGENT_OUTPUT] Successfully stored research analysis output with requestId: ${requestId}`);

      // Add the requestId to the result for reference
      (result as any).requestId = requestId;
    } catch (storageError) {
      console.error(`[RESEARCH_AGENT_OUTPUT] Error storing research analysis output:`, storageError);
      // Continue with the response even if storage fails
    }
  }

  return {
    success: result.success,
    taskId: result.taskId,
    output: result.output,
    outputDocumentIds: result.outputDocumentIds,
    researchSpecificOutput: (result as any).researchSpecificOutput,
    metadata: {
      source: 'Research Team Agent',
      pmoId: metadata.pmoId,
      processedAt: new Date().toISOString(),
      teamId: AgenticTeamId.Research,
      methodology: (result as any).researchSpecificOutput?.researchMethodology
    },
    error: result.error,
    requestId: (result as any).requestId // Include requestId for tracking
  };
}

/**
 * Process direct research request using ResearchAgentManager
 */
async function processDirectResearchRequest(requestBody: any, userId: string) {
  const { prompt, context, category, metadata } = requestBody;

  console.log('[Research API] Processing direct research request');

  // Initialize ResearchAgentManager
  const researchManager = new ResearchAgentManager({
    userId,
    defaultLlmProvider: 'anthropic',
    defaultLlmModel: 'claude-sonnet-4-20250514'
  });

  await researchManager.initializeResearchTeam();

  // Create research task request
  const researchRequest = {
    taskId: `research-${Date.now()}`,
    topic: metadata?.projectTitle || 'Research Analysis',
    scope: prompt,
    requiredDepth: 'moderate' as const,
    outputFormat: 'report' as const,
    deadline: metadata?.deadline ? new Date(metadata.deadline) : undefined,
    requesterInfo: `Direct Request - ${userId}`
  };

  // Start research workflow
  const researchPlanId = await researchManager.startResearchTask(researchRequest);

  // Generate output summary
  const output = `# Research Analysis Initiated

## Research Request
- **Topic**: ${researchRequest.topic}
- **Scope**: ${researchRequest.scope}
- **Research Plan ID**: ${researchPlanId}
- **Initiated**: ${new Date().toISOString()}

## Research Workflow
The Research team has initiated a comprehensive analysis workflow:
1. **Information Retrieval**: Gathering relevant data and sources
2. **Data Analysis**: Synthesizing findings and identifying patterns
3. **Report Writing**: Creating structured research deliverables
4. **Quality Assurance**: Reviewing and validating results

## Next Steps
The research team will execute the analysis and provide detailed findings upon completion.
Research deliverables will be available through the research plan: ${researchPlanId}

---
*Generated by Research Agent Collaboration API*`;

  return {
    success: true,
    taskId: researchRequest.taskId,
    output,
    outputDocumentIds: [researchPlanId],
    researchSpecificOutput: {
      researchPlanId,
      researchMethodology: 'Standard Research Workflow',
      qualityAssurance: 'Research team QA process initiated'
    },
    metadata: {
      source: 'Research Agent Manager',
      processedAt: new Date().toISOString(),
      teamId: AgenticTeamId.Research,
      methodology: 'Direct Research Request'
    }
  };
}

/**
 * GET endpoint for research collaboration status and capabilities
 */
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Return research team capabilities and status
    const capabilities = {
      teamId: AgenticTeamId.Research,
      teamName: 'Research Team',
      capabilities: [
        'Information retrieval and analysis',
        'Data synthesis and insights generation',
        'Report writing and formatting',
        'Quality assurance and review',
        'PMO compliance and standards adherence',
        'Cross-functional coordination',
        'Strategic planning and recommendations'
      ],
      supportedFormats: ['summary', 'report', 'presentation'],
      supportedDepths: ['surface', 'moderate', 'deep'],
      pmoIntegration: {
        enabled: true,
        features: [
          'PMO task processing',
          'Strategic implementation planning',
          'Cross-team coordination',
          'PMO document standards compliance'
        ]
      },
      apiVersion: '1.0.0',
      lastUpdated: new Date().toISOString()
    };

    return NextResponse.json({
      success: true,
      capabilities
    });

  } catch (error: any) {
    console.error('[Research API] Error getting capabilities:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to get research capabilities' },
      { status: 500 }
    );
  }
}
